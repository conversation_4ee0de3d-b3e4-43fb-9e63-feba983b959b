// Newsletter Subscription JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initNewsletterForm();
    initFooterNewsletter();
});

// Initialize main newsletter form
function initNewsletterForm() {
    const form = document.getElementById('newsletterForm');
    if (!form) return;

    // Add real-time validation
    const inputs = form.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateNewsletterField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateNewsletterField(this);
            }
        });
    });

    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateNewsletterForm(form)) {
            submitNewsletterForm(form);
        }
    });
}

// Initialize footer newsletter form
function initFooterNewsletter() {
    const footerForm = document.getElementById('footerNewsletterForm');
    if (!footerForm) return;

    footerForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const emailInput = this.querySelector('input[type="email"]');
        if (emailInput && isValidEmail(emailInput.value)) {
            submitFooterNewsletter(emailInput.value);
        } else {
            showToast('Please enter a valid email address', 'danger');
        }
    });
}

// Validate newsletter field
function validateNewsletterField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // Clear previous validation
    field.classList.remove('is-invalid', 'is-valid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.remove();
    }

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required.';
    }
    // Email validation
    else if (field.type === 'email' && value && !isValidEmail(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address.';
    }
    // Checkbox validation
    else if (field.type === 'checkbox' && field.hasAttribute('required') && !field.checked) {
        isValid = false;
        errorMessage = 'Please agree to receive newsletters.';
    }

    // Apply validation result
    if (isValid) {
        field.classList.add('is-valid');
    } else {
        field.classList.add('is-invalid');
        
        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = errorMessage;
        field.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

// Validate newsletter form
function validateNewsletterForm(form) {
    const fields = form.querySelectorAll('input[required]');
    let isFormValid = true;

    fields.forEach(field => {
        if (!validateNewsletterField(field)) {
            isFormValid = false;
        }
    });

    return isFormValid;
}

// Submit newsletter form
function submitNewsletterForm(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Subscribing...';
    submitBtn.disabled = true;

    // Collect form data
    const formData = new FormData(form);
    
    // Add source information
    formData.append('source', 'homepage');
    formData.append('action', 'newsletter_subscribe');

    // Submit to server
    fetch('newsletter-subscribe.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (data.success) {
            // Show success message
            showToast(data.message || 'Thank you for subscribing! Please check your email to verify your subscription.', 'success');
            
            // Reset form
            form.reset();
            form.classList.remove('was-validated');
            
            // Remove validation classes
            const fields = form.querySelectorAll('.is-valid, .is-invalid');
            fields.forEach(field => {
                field.classList.remove('is-valid', 'is-invalid');
            });

            // Track subscription event
            trackNewsletterSubscription(data.email);
            
        } else {
            showToast(data.message || 'Sorry, there was an error. Please try again.', 'danger');
        }
    })
    .catch(error => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        console.error('Newsletter subscription error:', error);
        showToast('Sorry, there was an error. Please try again.', 'danger');
    });
}

// Submit footer newsletter
function submitFooterNewsletter(email) {
    const formData = new FormData();
    formData.append('email', email);
    formData.append('source', 'footer');
    formData.append('action', 'newsletter_subscribe');

    // Show loading in button
    const submitBtn = document.querySelector('#footerNewsletterForm button');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';
    submitBtn.disabled = true;

    fetch('newsletter-subscribe.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (data.success) {
            showToast(data.message || 'Thank you for subscribing!', 'success');
            document.getElementById('footerNewsletterForm').reset();
            trackNewsletterSubscription(email);
        } else {
            showToast(data.message || 'Sorry, there was an error. Please try again.', 'danger');
        }
    })
    .catch(error => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        console.error('Footer newsletter error:', error);
        showToast('Sorry, there was an error. Please try again.', 'danger');
    });
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Track newsletter subscription (for analytics)
function trackNewsletterSubscription(email) {
    // Track with Google Analytics if available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'newsletter_subscription', {
            'event_category': 'engagement',
            'event_label': 'newsletter',
            'value': 1
        });
    }

    // Track with Facebook Pixel if available
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Subscribe', {
            content_name: 'Newsletter'
        });
    }

    // Custom tracking
    console.log('Newsletter subscription tracked:', email);
}

// Toast notification function
function showToast(message, type = 'success') {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast');
    existingToasts.forEach(toast => toast.remove());

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    const iconClass = type === 'success' ? 'fa-check-circle' : 
                     type === 'danger' ? 'fa-exclamation-triangle' : 
                     'fa-info-circle';
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas ${iconClass} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 5000
    });
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// Newsletter popup (optional feature)
function initNewsletterPopup() {
    // Check if user has already subscribed or dismissed popup
    const hasSubscribed = localStorage.getItem('newsletter_subscribed');
    const hasDismissed = localStorage.getItem('newsletter_popup_dismissed');
    const lastShown = localStorage.getItem('newsletter_popup_last_shown');
    
    // Don't show if already subscribed or dismissed recently
    if (hasSubscribed || (hasDismissed && Date.now() - parseInt(lastShown) < 24 * 60 * 60 * 1000)) {
        return;
    }

    // Show popup after 30 seconds or on scroll
    let popupShown = false;
    
    const showPopup = () => {
        if (popupShown) return;
        popupShown = true;
        
        // Create and show newsletter popup
        createNewsletterPopup();
        localStorage.setItem('newsletter_popup_last_shown', Date.now().toString());
    };

    // Show after 30 seconds
    setTimeout(showPopup, 30000);

    // Show on scroll (50% of page)
    window.addEventListener('scroll', () => {
        const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
        if (scrollPercent > 50) {
            showPopup();
        }
    });
}

// Create newsletter popup
function createNewsletterPopup() {
    const popup = document.createElement('div');
    popup.className = 'newsletter-popup';
    popup.innerHTML = `
        <div class="newsletter-popup-content">
            <button class="newsletter-popup-close">&times;</button>
            <div class="newsletter-popup-header">
                <h3>🎉 Stay Updated!</h3>
                <p>Get exclusive web development tips and offers</p>
            </div>
            <form class="newsletter-popup-form">
                <input type="email" placeholder="Enter your email" required>
                <button type="submit">Subscribe</button>
            </form>
            <p class="newsletter-popup-note">No spam, unsubscribe anytime</p>
        </div>
    `;

    document.body.appendChild(popup);

    // Handle popup interactions
    const closeBtn = popup.querySelector('.newsletter-popup-close');
    const form = popup.querySelector('.newsletter-popup-form');

    closeBtn.addEventListener('click', () => {
        popup.remove();
        localStorage.setItem('newsletter_popup_dismissed', 'true');
    });

    form.addEventListener('submit', (e) => {
        e.preventDefault();
        const email = form.querySelector('input').value;
        if (isValidEmail(email)) {
            submitFooterNewsletter(email);
            popup.remove();
            localStorage.setItem('newsletter_subscribed', 'true');
        }
    });

    // Close on outside click
    popup.addEventListener('click', (e) => {
        if (e.target === popup) {
            popup.remove();
            localStorage.setItem('newsletter_popup_dismissed', 'true');
        }
    });
}

// Initialize popup (uncomment to enable)
// document.addEventListener('DOMContentLoaded', initNewsletterPopup);
