/* Custom CSS for WebsiteDeveloper0002.in */

:root {
    --primary-color: #0d6efd;
    --primary-dark: #0b5ed7;
    --primary-light: #6ea8fe;
    --secondary-color: #ffc107;
    --secondary-dark: #ffca2c;
    --secondary-light: #fff3cd;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #fd7e14;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.7;
    color: var(--gray-800);
    overflow-x: hidden;
    background-color: #ffffff;
    font-size: 16px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
}

/* Navigation Styles */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(13, 110, 253, 0.95) !important;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    padding: 1rem 0;
}

.navbar.scrolled {
    padding: 0.5rem 0;
    background: rgba(13, 110, 253, 0.98) !important;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-brand i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--secondary-color) !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-icon {
    font-size: 15rem;
    color: rgba(255, 255, 255, 0.1);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Enhanced Service Cards */
.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: var(--transition);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-card h4 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.service-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Feature List */
.feature-list {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature-item i {
    margin-right: 1rem;
    font-size: 1.2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #6c757d;
    font-weight: 500;
}

/* Pricing Cards */
.pricing-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.pricing-card.featured {
    border: 3px solid var(--secondary-color);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: 'POPULAR';
    position: absolute;
    top: 20px;
    right: -30px;
    background: var(--secondary-color);
    color: var(--dark-color);
    padding: 5px 40px;
    font-weight: bold;
    font-size: 0.8rem;
    transform: rotate(45deg);
}

.pricing-card h4 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.price {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.pricing-card p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* Enhanced Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 2rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--box-shadow);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-lg);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    color: var(--dark-color);
    box-shadow: var(--box-shadow);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color));
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-lg);
    color: var(--dark-color);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    border-radius: var(--border-radius-lg);
}

.btn-sm {
    padding: 0.5rem 1.5rem;
    font-size: 0.9rem;
}

/* Button Groups */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* Floating Action Button */
.btn-floating {
    border-radius: 50%;
    width: 56px;
    height: 56px;
    padding: 0;
    box-shadow: var(--box-shadow-lg);
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.btn-floating:hover {
    transform: scale(1.1);
}

/* Footer */
footer {
    background: var(--dark-color) !important;
}

footer h5 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--secondary-color) !important;
}

.social-links a {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }

    .hero-icon {
        font-size: 8rem;
        margin-top: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .pricing-card.featured {
        transform: none;
        margin-top: 1rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .display-5 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        min-height: 80vh;
    }

    .service-card,
    .pricing-card {
        margin-bottom: 2rem;
    }

    .btn-lg {
        padding: 10px 25px;
        font-size: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scroll to Top Button */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    background: var(--secondary-color);
    color: var(--dark-color);
    transform: translateY(-3px);
}

/* Page Header */
.page-header {
    padding-top: 100px;
    min-height: 300px;
    display: flex;
    align-items: center;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.6);
}

/* Service Detail Cards */
.service-detail-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border: 1px solid #e9ecef;
}

.service-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.service-icon-small {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.service-icon-small i {
    font-size: 1.5rem;
    color: white;
}

.service-detail-card h5 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li:before {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--success-color);
    position: absolute;
    left: 0;
    top: 0.5rem;
}

.service-features li:last-child {
    border-bottom: none;
}

/* Process Steps */
.process-step {
    text-align: center;
    padding: 2rem 1rem;
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    font-weight: bold;
    color: white;
}

.process-step h5 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.process-step p {
    color: #6c757d;
    line-height: 1.6;
}

/* Pricing Plans */
.pricing-plan {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
    height: 100%;
    border: 2px solid transparent;
}

.pricing-plan:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.pricing-plan.featured {
    border-color: var(--secondary-color);
    transform: scale(1.05);
}

.pricing-plan.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.popular-badge {
    position: absolute;
    top: 20px;
    right: -30px;
    background: var(--secondary-color);
    color: var(--dark-color);
    padding: 8px 40px;
    font-weight: bold;
    font-size: 0.9rem;
    transform: rotate(45deg);
    z-index: 10;
}

.plan-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
}

.plan-header h3 {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.plan-price {
    margin-bottom: 1rem;
}

.plan-price .currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.plan-price .amount {
    font-size: 3rem;
    font-weight: bold;
}

.plan-price .period {
    font-size: 1rem;
    opacity: 0.8;
}

.plan-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.plan-features {
    padding: 2rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    align-items: center;
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features li i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.plan-features li i.fa-check {
    color: var(--success-color);
}

.plan-footer {
    padding: 0 2rem 2rem;
}

/* Addon Services */
.addon-service {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.addon-service:hover {
    transform: translateY(-5px);
}

.addon-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.addon-icon i {
    font-size: 1.8rem;
    color: white;
}

.addon-service h5 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.addon-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.addon-service p {
    color: #6c757d;
    line-height: 1.6;
}

/* Accordion Customization */
.accordion-item {
    border: 1px solid #e9ecef;
    border-radius: 10px !important;
    margin-bottom: 1rem;
    overflow: hidden;
}

.accordion-button {
    background: var(--light-color);
    border: none;
    font-weight: 600;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-body {
    padding: 1.5rem;
    background: white;
}

/* Contact Form */
.contact-form-wrapper {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.contact-form-wrapper h2 {
    color: var(--dark-color);
    font-weight: bold;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius);
    padding: 1rem;
    transition: var(--transition);
    background: var(--gray-100);
    font-size: 1rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    background: white;
    transform: translateY(-2px);
}

.form-control:hover,
.form-select:hover {
    border-color: var(--gray-400);
    background: white;
}

.form-check-input {
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius);
    width: 1.25rem;
    height: 1.25rem;
    transition: var(--transition);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
}

.form-check-label {
    font-weight: 500;
    color: var(--dark-color);
}

/* Contact Info */
.contact-info {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #f8f9fa;
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: 1.5rem;
    color: white;
}

.contact-details h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: #6c757d;
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.contact-details small {
    color: #adb5bd;
    font-style: italic;
}

/* Social Media Links */
.social-media h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: white;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    color: white;
}

.social-link.facebook {
    background: #3b5998;
}

.social-link.twitter {
    background: #1da1f2;
}

.social-link.linkedin {
    background: #0077b5;
}

.social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-link.youtube {
    background: #ff0000;
}

/* Map Container */
.map-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* About Page Styles */
.about-stat {
    text-align: center;
    padding: 1rem;
}

.about-stat h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.about-stat p {
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

.about-image {
    text-align: center;
}

.image-placeholder {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 20px;
    padding: 4rem 2rem;
    color: white;
    text-align: center;
}

.image-placeholder i {
    font-size: 5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.image-placeholder p {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

/* Mission Cards */
.mission-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    height: 100%;
    transition: transform 0.3s ease;
}

.mission-card:hover {
    transform: translateY(-10px);
}

.mission-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
}

.mission-icon i {
    font-size: 2rem;
    color: white;
}

.mission-card h3 {
    color: var(--dark-color);
    font-weight: bold;
    margin-bottom: 1.5rem;
}

.mission-card p {
    color: #6c757d;
    line-height: 1.8;
}

/* Value Cards */
.value-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.value-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.value-icon i {
    font-size: 1.8rem;
    color: white;
}

.value-card h4 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.value-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Why Choose Items */
.why-choose-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.why-choose-item i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-right: 1rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.why-choose-item h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.why-choose-item p {
    margin: 0;
    opacity: 0.9;
}

/* Testimonial Cards */
.testimonial-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    height: 100%;
    position: relative;
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-content {
    margin-bottom: 2rem;
}

.testimonial-content i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.testimonial-content p {
    font-style: italic;
    color: #6c757d;
    line-height: 1.8;
    margin: 0;
}

.testimonial-author {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #f8f9fa;
}

.testimonial-author h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.testimonial-author small {
    color: #adb5bd;
}

/* Portfolio Page Styles */
.portfolio-stat {
    text-align: center;
    padding: 2rem 1rem;
}

.portfolio-stat h3 {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.portfolio-stat p {
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

/* Portfolio Filter */
.portfolio-filter {
    margin-bottom: 3rem;
}

.filter-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: var(--dark-color);
    padding: 0.75rem 1.5rem;
    margin: 0 0.5rem 0.5rem 0;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Portfolio Cards */
.portfolio-item {
    transition: all 0.3s ease;
}

.portfolio-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.portfolio-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.portfolio-image .image-placeholder {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
}

.portfolio-image .image-placeholder i {
    font-size: 4rem;
    opacity: 0.8;
}

.portfolio-image .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-image:hover .overlay {
    opacity: 1;
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-content h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.portfolio-content p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.portfolio-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: var(--light-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Technology Items */
.tech-item {
    text-align: center;
    padding: 1.5rem 1rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-5px);
}

.tech-item i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.tech-item p {
    color: var(--dark-color);
    font-weight: 600;
    margin: 0;
}

/* Load More Button */
#loadMoreBtn {
    transition: all 0.3s ease;
}

#loadMoreBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(13, 110, 253, 0.3);
}

/* Legal Pages Styles */
.legal-content {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.legal-content h2 {
    color: var(--primary-color);
    font-weight: bold;
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light-color);
}

.legal-content h2:first-of-type {
    margin-top: 0;
}

.legal-content h3 {
    color: var(--dark-color);
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.legal-content h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.legal-content h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.legal-content p {
    color: #6c757d;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.legal-content ul {
    margin-bottom: 1.5rem;
}

.legal-content li {
    color: #6c757d;
    line-height: 1.7;
    margin-bottom: 0.5rem;
}

.legal-content strong {
    color: var(--dark-color);
    font-weight: 600;
}

.last-updated {
    text-align: right;
    font-style: italic;
}

/* Legal Info Boxes */
.legal-content .bg-light,
.legal-content .bg-warning,
.legal-content .bg-primary,
.legal-content .bg-success,
.legal-content .bg-danger,
.legal-content .bg-info {
    border-radius: 15px;
    border-left: 5px solid;
}

.legal-content .bg-light {
    border-left-color: var(--primary-color);
}

.legal-content .bg-warning {
    border-left-color: #ff6b35;
}

.legal-content .bg-primary {
    border-left-color: var(--secondary-color);
}

.legal-content .bg-success {
    border-left-color: #28a745;
}

.legal-content .bg-danger {
    border-left-color: #dc3545;
}

.legal-content .bg-info {
    border-left-color: #17a2b8;
}

/* Timeline Table Styles */
.timeline-table {
    margin: 2rem 0;
}

.timeline-table .table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.timeline-table .table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.timeline-table .table td {
    padding: 1rem;
    border-color: #e9ecef;
    vertical-align: middle;
}

.timeline-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Legal Navigation */
.legal-nav {
    position: sticky;
    top: 100px;
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.legal-nav h5 {
    color: var(--primary-color);
    font-weight: bold;
    margin-bottom: 1rem;
}

.legal-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.legal-nav li {
    margin-bottom: 0.5rem;
}

.legal-nav a {
    color: #6c757d;
    text-decoration: none;
    padding: 0.5rem 0;
    display: block;
    transition: color 0.3s ease;
}

.legal-nav a:hover {
    color: var(--primary-color);
    padding-left: 0.5rem;
}

/* Newsletter Styles */
.newsletter-benefits {
    list-style: none;
    padding: 0;
    margin: 0;
}

.newsletter-benefits li {
    padding: 0.5rem 0;
    font-size: 1.1rem;
}

.newsletter-benefits i {
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

.newsletter-form-wrapper {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.newsletter-form-wrapper h4 {
    color: var(--dark-color);
    font-weight: 600;
}

.newsletter-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.newsletter-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.newsletter-form .form-check-input {
    border: 2px solid #e9ecef;
    border-radius: 5px;
}

.newsletter-form .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.newsletter-form .form-check-label {
    font-weight: 500;
    color: var(--dark-color);
}

.newsletter-note {
    text-align: center;
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
}

.newsletter-note i {
    color: var(--success-color);
    margin-right: 0.5rem;
}

/* Footer Newsletter */
.footer-newsletter {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    margin-top: 1rem;
}

.footer-newsletter h6 {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-newsletter .input-group {
    margin-bottom: 1rem;
}

.footer-newsletter .form-control {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 25px 0 0 25px;
}

.footer-newsletter .btn {
    border-radius: 0 25px 25px 0;
    border: none;
}

/* Responsive Newsletter */
@media (max-width: 768px) {
    .newsletter-form-wrapper {
        padding: 2rem 1.5rem;
    }

    .newsletter-benefits {
        margin-bottom: 2rem;
    }

    .footer-newsletter .input-group {
        flex-direction: column;
    }

    .footer-newsletter .form-control,
    .footer-newsletter .btn {
        border-radius: 25px;
        margin-bottom: 0.5rem;
    }
}

/* Responsive Legal Content */
@media (max-width: 768px) {
    .legal-content {
        padding: 2rem 1.5rem;
    }

    .legal-content h2 {
        font-size: 1.5rem;
        margin-top: 2rem;
    }

    .legal-content h3 {
        font-size: 1.25rem;
    }

    .timeline-table {
        overflow-x: auto;
    }

    .timeline-table .table {
        min-width: 600px;
    }
}

/* Enhanced Animations and Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-15px);
    }
    70% {
        transform: translateY(-7px);
    }
    90% {
        transform: translateY(-3px);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(13, 110, 253, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(13, 110, 253, 0.8);
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 2s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: var(--transition);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -200%;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

/* Scroll Animations */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.scroll-animate.animate {
    opacity: 1;
    transform: translateY(0);
}

/* Parallax Effect */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* Glass Morphism Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Enhanced Shadows */
.shadow-soft {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);
}

.shadow-hard {
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.16);
}

.shadow-colored {
    box-shadow: 0 10px 30px rgba(13, 110, 253, 0.3);
}

/* Utility Classes */
.overflow-hidden {
    overflow: hidden;
}

.position-relative {
    position: relative;
}

.z-index-1 {
    z-index: 1;
}

.z-index-2 {
    z-index: 2;
}

.z-index-3 {
    z-index: 3;
}

/* Performance Optimizations */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

.smooth-scroll {
    scroll-behavior: smooth;
}

/* Dark Mode Support (Future) */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-color: #f8f9fa;
        --light-color: #212529;
        --gray-100: #343a40;
        --gray-200: #495057;
        --gray-300: #6c757d;
    }
}
