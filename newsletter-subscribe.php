<?php
/**
 * Newsletter Subscription Handler
 * WebsiteDeveloper0002.in
 */

// Include required files
require_once 'includes/config.php';
require_once 'includes/EmailHandler.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Initialize response array
$response = array(
    'success' => false,
    'message' => '',
    'email' => ''
);

// Check if form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    echo json_encode($response);
    exit;
}

// Rate limiting
$clientIP = getClientIP();
if (!checkRateLimit('newsletter_' . $clientIP, 3, 300)) { // 3 attempts per 5 minutes
    $response['message'] = 'Too many subscription attempts. Please try again later.';
    echo json_encode($response);
    exit;
}

// Get and validate input data
$email = sanitizeInput($_POST['email'] ?? '');
$name = sanitizeInput($_POST['name'] ?? '');
$source = sanitizeInput($_POST['source'] ?? 'homepage');
$agree = isset($_POST['agree']);

// Validation
if (empty($email)) {
    $response['message'] = 'Email address is required.';
    echo json_encode($response);
    exit;
}

if (!isValidEmail($email)) {
    $response['message'] = 'Please enter a valid email address.';
    echo json_encode($response);
    exit;
}

if ($source === 'homepage' && !$agree) {
    $response['message'] = 'Please agree to receive newsletters.';
    echo json_encode($response);
    exit;
}

try {
    $db = Database::getInstance();
    $emailHandler = new EmailHandler();
    
    // Check if email already exists
    $existingSubscriber = $db->selectOne(
        'newsletter_subscribers', 
        'email = ?', 
        [$email]
    );
    
    if ($existingSubscriber) {
        if ($existingSubscriber['status'] === 'active') {
            $response['message'] = 'You are already subscribed to our newsletter!';
            $response['email'] = $email;
            echo json_encode($response);
            exit;
        } elseif ($existingSubscriber['status'] === 'pending') {
            $response['message'] = 'Please check your email and verify your subscription.';
            $response['email'] = $email;
            echo json_encode($response);
            exit;
        } elseif ($existingSubscriber['status'] === 'unsubscribed') {
            // Reactivate subscription
            $verificationToken = generateToken();
            $unsubscribeToken = generateToken();
            
            $updateData = [
                'name' => $name,
                'status' => 'pending',
                'verification_token' => $verificationToken,
                'unsubscribe_token' => $unsubscribeToken,
                'subscription_source' => $source,
                'ip_address' => $clientIP,
                'user_agent' => getUserAgent(),
                'unsubscribed_at' => null,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $db->update('newsletter_subscribers', $updateData, 'id = ?', [$existingSubscriber['id']]);
            
            // Send verification email
            $emailHandler->sendNewsletterVerification($email, $name, $verificationToken);
            
            $response['success'] = true;
            $response['message'] = 'Welcome back! Please check your email to verify your subscription.';
            $response['email'] = $email;
            
            logActivity('newsletter_resubscribe', "Email: $email, Source: $source");
        }
    } else {
        // New subscription
        $verificationToken = generateToken();
        $unsubscribeToken = generateToken();
        
        $subscriptionData = [
            'email' => $email,
            'name' => $name,
            'subscription_source' => $source,
            'status' => 'pending',
            'verification_token' => $verificationToken,
            'unsubscribe_token' => $unsubscribeToken,
            'ip_address' => $clientIP,
            'user_agent' => getUserAgent()
        ];
        
        $subscriberId = $db->insert('newsletter_subscribers', $subscriptionData);
        
        if ($subscriberId) {
            // Send verification email
            $emailHandler->sendNewsletterVerification($email, $name, $verificationToken);
            
            $response['success'] = true;
            $response['message'] = 'Thank you for subscribing! Please check your email to verify your subscription.';
            $response['email'] = $email;
            
            logActivity('newsletter_subscribe', "Email: $email, Source: $source, ID: $subscriberId");
        } else {
            throw new Exception('Failed to save subscription');
        }
    }
    
} catch (Exception $e) {
    logActivity('newsletter_error', $e->getMessage());
    $response['message'] = 'Sorry, there was an error processing your subscription. Please try again.';
}

// Return JSON response
echo json_encode($response);
?>
