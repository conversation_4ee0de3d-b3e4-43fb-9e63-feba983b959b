<?php
/**
 * Email Queue Processor
 * WebsiteDeveloper0002.in
 * 
 * This script should be run via cron job every few minutes
 * Example cron: */5 * * * * /usr/bin/php /path/to/process-email-queue.php
 */

// Include required files
require_once dirname(__DIR__) . '/includes/config.php';
require_once dirname(__DIR__) . '/includes/EmailHandler.php';

// Set execution time limit
set_time_limit(300); // 5 minutes

// Log start
logActivity('email_queue_processor_start', 'Email queue processing started');

try {
    $emailHandler = new EmailHandler();
    
    // Process emails in batches
    $batchSize = 10;
    $maxBatches = 5; // Maximum 50 emails per run
    $totalProcessed = 0;
    
    echo "Starting email queue processing...\n";
    
    for ($batch = 0; $batch < $maxBatches; $batch++) {
        $processed = $emailHandler->processQueue($batchSize);
        $totalProcessed += $processed;
        
        echo "Batch " . ($batch + 1) . ": Processed $processed emails\n";
        
        // If no emails were processed, break the loop
        if ($processed === 0) {
            break;
        }
        
        // Small delay between batches
        sleep(1);
    }
    
    echo "Email queue processing completed. Total processed: $totalProcessed\n";
    
    // Get queue statistics
    $stats = $emailHandler->getQueueStats();
    echo "Queue stats: " . json_encode($stats) . "\n";
    
    logActivity('email_queue_processor_complete', "Processed: $totalProcessed emails, Stats: " . json_encode($stats));
    
} catch (Exception $e) {
    $errorMessage = "Email queue processing failed: " . $e->getMessage();
    echo $errorMessage . "\n";
    logActivity('email_queue_processor_error', $errorMessage);
    
    // Send alert to admin if critical error
    if (strpos($e->getMessage(), 'database') !== false) {
        error_log($errorMessage);
    }
}

// Clean up old processed emails (optional)
try {
    $db = Database::getInstance();
    
    // Delete sent emails older than 30 days
    $deleted = $db->query(
        "DELETE FROM email_queue 
         WHERE status = 'sent' 
         AND sent_at < DATE_SUB(NOW(), INTERVAL 30 DAY)"
    )->rowCount();
    
    if ($deleted > 0) {
        echo "Cleaned up $deleted old sent emails\n";
        logActivity('email_queue_cleanup', "Deleted $deleted old sent emails");
    }
    
    // Delete failed emails older than 7 days
    $deletedFailed = $db->query(
        "DELETE FROM email_queue 
         WHERE status = 'failed' 
         AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)"
    )->rowCount();
    
    if ($deletedFailed > 0) {
        echo "Cleaned up $deletedFailed old failed emails\n";
        logActivity('email_queue_cleanup', "Deleted $deletedFailed old failed emails");
    }
    
} catch (Exception $e) {
    echo "Cleanup failed: " . $e->getMessage() . "\n";
    logActivity('email_queue_cleanup_error', $e->getMessage());
}

echo "Email queue processor finished.\n";
?>
