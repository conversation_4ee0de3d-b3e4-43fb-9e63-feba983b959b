<?php
// Contact Form Processing Script
// WebsiteDeveloper0002.in

// Set content type for JSON response
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Initialize response array
$response = array(
    'success' => false,
    'message' => '',
    'errors' => array()
);

// Check if form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    echo json_encode($response);
    exit;
}

// Sanitize and validate input data
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validatePhone($phone) {
    $cleanPhone = preg_replace('/\D/', '', $phone);
    return preg_match('/^[6-9]\d{9}$/', $cleanPhone);
}

// Get form data
$firstName = sanitizeInput($_POST['firstName'] ?? '');
$lastName = sanitizeInput($_POST['lastName'] ?? '');
$email = sanitizeInput($_POST['email'] ?? '');
$phone = sanitizeInput($_POST['phone'] ?? '');
$company = sanitizeInput($_POST['company'] ?? '');
$package = sanitizeInput($_POST['package'] ?? '');
$services = $_POST['services'] ?? array();
$budget = sanitizeInput($_POST['budget'] ?? '');
$message = sanitizeInput($_POST['message'] ?? '');
$agree = isset($_POST['agree']);

// Validation
$errors = array();

if (empty($firstName)) {
    $errors['firstName'] = 'First name is required.';
}

if (empty($lastName)) {
    $errors['lastName'] = 'Last name is required.';
}

if (empty($email)) {
    $errors['email'] = 'Email is required.';
} elseif (!validateEmail($email)) {
    $errors['email'] = 'Please enter a valid email address.';
}

if (empty($phone)) {
    $errors['phone'] = 'Phone number is required.';
} elseif (!validatePhone($phone)) {
    $errors['phone'] = 'Please enter a valid Indian phone number.';
}

if (empty($message)) {
    $errors['message'] = 'Project details are required.';
}

if (!$agree) {
    $errors['agree'] = 'You must agree to the terms and conditions.';
}

// If there are validation errors, return them
if (!empty($errors)) {
    $response['errors'] = $errors;
    $response['message'] = 'Please fix the errors and try again.';
    echo json_encode($response);
    exit;
}

// Prepare email content
$to = '<EMAIL>';
$subject = 'New Contact Form Submission - WebsiteDeveloper0002.in';

// Create HTML email content
$emailContent = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>New Contact Form Submission</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #0d6efd; color: white; padding: 20px; text-align: center; }
        .content { background: #f8f9fa; padding: 20px; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #495057; }
        .value { margin-top: 5px; padding: 10px; background: white; border-radius: 5px; }
        .services { display: flex; flex-wrap: wrap; gap: 10px; }
        .service-tag { background: #0d6efd; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h2>New Contact Form Submission</h2>
            <p>WebsiteDeveloper0002.in</p>
        </div>
        <div class='content'>
            <div class='field'>
                <div class='label'>Name:</div>
                <div class='value'>{$firstName} {$lastName}</div>
            </div>
            
            <div class='field'>
                <div class='label'>Email:</div>
                <div class='value'>{$email}</div>
            </div>
            
            <div class='field'>
                <div class='label'>Phone:</div>
                <div class='value'>{$phone}</div>
            </div>
            
            " . (!empty($company) ? "
            <div class='field'>
                <div class='label'>Company:</div>
                <div class='value'>{$company}</div>
            </div>
            " : "") . "
            
            " . (!empty($package) ? "
            <div class='field'>
                <div class='label'>Interested Package:</div>
                <div class='value'>" . ucfirst($package) . "</div>
            </div>
            " : "") . "
            
            " . (!empty($services) ? "
            <div class='field'>
                <div class='label'>Required Services:</div>
                <div class='value'>
                    <div class='services'>
                        " . implode('', array_map(function($service) {
                            return "<span class='service-tag'>" . ucfirst($service) . "</span>";
                        }, $services)) . "
                    </div>
                </div>
            </div>
            " : "") . "
            
            " . (!empty($budget) ? "
            <div class='field'>
                <div class='label'>Budget Range:</div>
                <div class='value'>{$budget}</div>
            </div>
            " : "") . "
            
            <div class='field'>
                <div class='label'>Project Details:</div>
                <div class='value'>" . nl2br($message) . "</div>
            </div>
            
            <div class='field'>
                <div class='label'>Submission Time:</div>
                <div class='value'>" . date('Y-m-d H:i:s') . "</div>
            </div>
        </div>
    </div>
</body>
</html>
";

// Email headers
$headers = array(
    'MIME-Version: 1.0',
    'Content-type: text/html; charset=UTF-8',
    'From: <EMAIL>',
    'Reply-To: ' . $email,
    'X-Mailer: PHP/' . phpversion()
);

// Send email
$mailSent = mail($to, $subject, $emailContent, implode("\r\n", $headers));

// Auto-reply to customer
$autoReplySubject = 'Thank you for contacting WebsiteDeveloper0002.in';
$autoReplyContent = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Thank you for your inquiry</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #0d6efd; color: white; padding: 20px; text-align: center; }
        .content { background: #f8f9fa; padding: 20px; }
        .footer { background: #212529; color: white; padding: 20px; text-align: center; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h2>Thank You for Your Inquiry!</h2>
            <p>WebsiteDeveloper0002.in</p>
        </div>
        <div class='content'>
            <p>Dear {$firstName},</p>
            
            <p>Thank you for contacting WebsiteDeveloper0002.in! We have received your inquiry and our team will review your requirements.</p>
            
            <p><strong>What happens next?</strong></p>
            <ul>
                <li>Our team will review your project details within 2 hours</li>
                <li>We will prepare a customized proposal for your requirements</li>
                <li>You will receive a detailed quote via email or phone call</li>
                <li>We will schedule a free consultation call to discuss your project</li>
            </ul>
            
            <p><strong>Need immediate assistance?</strong></p>
            <p>Call us at: <strong>+91 9876543210</strong><br>
            WhatsApp: <strong>+91 9876543210</strong><br>
            Email: <strong><EMAIL></strong></p>
            
            <p>We look forward to working with you and helping your business grow online!</p>
            
            <p>Best regards,<br>
            <strong>WebsiteDeveloper0002.in Team</strong></p>
        </div>
        <div class='footer'>
            <p>&copy; 2024 WebsiteDeveloper0002.in - Complete Web Development Solutions</p>
        </div>
    </div>
</body>
</html>
";

$autoReplyHeaders = array(
    'MIME-Version: 1.0',
    'Content-type: text/html; charset=UTF-8',
    'From: WebsiteDeveloper0002.in <<EMAIL>>',
    'X-Mailer: PHP/' . phpversion()
);

$autoReplySent = mail($email, $autoReplySubject, $autoReplyContent, implode("\r\n", $autoReplyHeaders));

// Log the submission (optional)
$logData = array(
    'timestamp' => date('Y-m-d H:i:s'),
    'name' => $firstName . ' ' . $lastName,
    'email' => $email,
    'phone' => $phone,
    'company' => $company,
    'package' => $package,
    'services' => implode(', ', $services),
    'budget' => $budget,
    'message' => substr($message, 0, 100) . '...',
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
);

// You can save to database or log file here
// file_put_contents('contact_logs.txt', json_encode($logData) . "\n", FILE_APPEND);

// Prepare response
if ($mailSent) {
    $response['success'] = true;
    $response['message'] = 'Thank you! Your message has been sent successfully. We will contact you soon.';
} else {
    $response['message'] = 'Sorry, there was an error sending your message. Please try again or contact us directly.';
}

// Return JSON response
echo json_encode($response);
?>
