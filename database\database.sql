-- WebsiteDeveloper0002.in Database Structure
-- MySQL Database Schema

-- Create Database
CREATE DATABASE IF NOT EXISTS websitedeveloper0002_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE websitedeveloper0002_db;

-- 1. Contact Form Submissions Table
CREATE TABLE contact_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    company VARCHAR(255),
    package ENUM('basic', 'standard', 'premium', 'custom') DEFAULT NULL,
    services JSON,
    budget ENUM('5000-10000', '10000-25000', '25000-50000', '50000+') DEFAULT NULL,
    message TEXT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    status ENUM('new', 'contacted', 'quoted', 'converted', 'closed') DEFAULT 'new',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_to VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_package (package)
);

-- 2. Newsletter Subscribers Table
CREATE TABLE newsletter_subscribers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(200),
    subscription_source ENUM('homepage', 'footer', 'contact_form', 'manual') DEFAULT 'homepage',
    status ENUM('active', 'unsubscribed', 'bounced', 'pending') DEFAULT 'pending',
    verification_token VARCHAR(100),
    verified_at TIMESTAMP NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    preferences JSON,
    unsubscribe_token VARCHAR(100) UNIQUE,
    unsubscribed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_verification_token (verification_token),
    INDEX idx_unsubscribe_token (unsubscribe_token)
);

-- 3. Email Templates Table
CREATE TABLE email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL UNIQUE,
    template_type ENUM('welcome', 'newsletter', 'contact_response', 'quote', 'reminder') NOT NULL,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT,
    variables JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_template_name (template_name),
    INDEX idx_template_type (template_type)
);

-- 4. Email Queue Table
CREATE TABLE email_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    to_email VARCHAR(255) NOT NULL,
    to_name VARCHAR(200),
    from_email VARCHAR(255) NOT NULL,
    from_name VARCHAR(200),
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT,
    template_id INT,
    priority ENUM('low', 'normal', 'high') DEFAULT 'normal',
    status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    error_message TEXT,
    scheduled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_to_email (to_email)
);

-- 5. Website Analytics Table
CREATE TABLE website_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_url VARCHAR(500) NOT NULL,
    page_title VARCHAR(255),
    visitor_ip VARCHAR(45),
    user_agent TEXT,
    referrer VARCHAR(500),
    session_id VARCHAR(100),
    visit_duration INT DEFAULT 0,
    actions_taken JSON,
    device_type ENUM('desktop', 'tablet', 'mobile') DEFAULT 'desktop',
    browser VARCHAR(100),
    os VARCHAR(100),
    country VARCHAR(100),
    city VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_page_url (page_url),
    INDEX idx_visitor_ip (visitor_ip),
    INDEX idx_created_at (created_at),
    INDEX idx_session_id (session_id)
);

-- 6. Projects Table
CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    contact_id INT,
    project_name VARCHAR(255) NOT NULL,
    client_name VARCHAR(255) NOT NULL,
    client_email VARCHAR(255) NOT NULL,
    client_phone VARCHAR(20),
    project_type ENUM('basic', 'standard', 'premium', 'custom') NOT NULL,
    project_status ENUM('inquiry', 'quoted', 'approved', 'in_progress', 'testing', 'completed', 'delivered', 'cancelled') DEFAULT 'inquiry',
    project_value DECIMAL(10,2),
    advance_paid DECIMAL(10,2) DEFAULT 0,
    balance_amount DECIMAL(10,2) DEFAULT 0,
    start_date DATE,
    expected_completion DATE,
    actual_completion DATE,
    project_description TEXT,
    requirements JSON,
    deliverables JSON,
    project_url VARCHAR(255),
    admin_notes TEXT,
    client_feedback TEXT,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contact_submissions(id) ON DELETE SET NULL,
    INDEX idx_client_email (client_email),
    INDEX idx_project_status (project_status),
    INDEX idx_project_type (project_type),
    INDEX idx_created_at (created_at)
);

-- 7. Admin Users Table
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(200) NOT NULL,
    role ENUM('super_admin', 'admin', 'manager', 'support') DEFAULT 'support',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
);

-- Insert Default Email Templates
INSERT INTO email_templates (template_name, template_type, subject, html_content, text_content, variables) VALUES

-- Welcome Email for Contact Form
('contact_welcome', 'contact_response', 'Thank you for contacting WebsiteDeveloper0002.in!', 
'<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>Welcome to WebsiteDeveloper0002.in</title></head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: #0d6efd; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="margin: 0; font-size: 28px;">🎉 Welcome to WebsiteDeveloper0002.in!</h1>
        <p style="margin: 10px 0 0; font-size: 16px;">Complete Web Development Solutions</p>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
        <p>Dear <strong>{{first_name}}</strong>,</p>
        <p>Thank you for contacting WebsiteDeveloper0002.in! We have received your inquiry and our team is excited to help you build your digital presence.</p>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <h3 style="color: #0d6efd; margin-top: 0;">📋 Your Inquiry Details:</h3>
            <p><strong>Package Interest:</strong> {{package}}</p>
            <p><strong>Budget Range:</strong> {{budget}}</p>
            <p><strong>Services Required:</strong> {{services}}</p>
        </div>
        
        <h3 style="color: #0d6efd;">🚀 What Happens Next?</h3>
        <ul style="padding-left: 20px;">
            <li>Our team will review your requirements within <strong>2 hours</strong></li>
            <li>We will prepare a customized proposal for your project</li>
            <li>You will receive a detailed quote via email or phone call</li>
            <li>We will schedule a free consultation to discuss your vision</li>
        </ul>
        
        <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h4 style="color: #0d6efd; margin-top: 0;">📞 Need Immediate Assistance?</h4>
            <p style="margin-bottom: 10px;"><strong>Call:</strong> +91 9876543210</p>
            <p style="margin-bottom: 10px;"><strong>WhatsApp:</strong> +91 9876543210</p>
            <p style="margin-bottom: 0;"><strong>Email:</strong> <EMAIL></p>
        </div>
        
        <p>We look forward to working with you and helping your business grow online!</p>
        
        <p style="margin-top: 30px;">Best regards,<br><strong>WebsiteDeveloper0002.in Team</strong></p>
    </div>
    <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
        <p>&copy; 2024 WebsiteDeveloper0002.in - Complete Web Development Solutions</p>
    </div>
</body>
</html>',
'Dear {{first_name}},

Thank you for contacting WebsiteDeveloper0002.in! We have received your inquiry.

Your Inquiry Details:
- Package Interest: {{package}}
- Budget Range: {{budget}}
- Services Required: {{services}}

What Happens Next?
- Our team will review your requirements within 2 hours
- We will prepare a customized proposal for your project
- You will receive a detailed quote via email or phone call
- We will schedule a free consultation to discuss your vision

Need Immediate Assistance?
Call: +91 9876543210
WhatsApp: +91 9876543210
Email: <EMAIL>

Best regards,
WebsiteDeveloper0002.in Team',
'["first_name", "package", "budget", "services"]'),

-- Newsletter Welcome Email
('newsletter_welcome', 'welcome', 'Welcome to WebsiteDeveloper0002.in Newsletter! 🎉', 
'<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>Newsletter Welcome</title></head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #0d6efd, #ffc107); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="margin: 0; font-size: 28px;">📧 Newsletter Subscription Confirmed!</h1>
        <p style="margin: 10px 0 0; font-size: 16px;">Stay Updated with Latest Web Development Trends</p>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
        <p>Hello <strong>{{name}}</strong>,</p>
        <p>Welcome to the WebsiteDeveloper0002.in newsletter family! 🎉</p>
        <p>You have successfully subscribed to our newsletter and will now receive:</p>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #0d6efd; margin-top: 0;">📬 What You Will Receive:</h3>
            <ul style="padding-left: 20px; margin: 0;">
                <li><strong>Weekly Web Development Tips</strong> - Latest trends and best practices</li>
                <li><strong>Exclusive Offers</strong> - Special discounts on our services</li>
                <li><strong>Industry Insights</strong> - Digital marketing and SEO tips</li>
                <li><strong>Case Studies</strong> - Success stories from our clients</li>
                <li><strong>New Service Announcements</strong> - Be the first to know</li>
            </ul>
        </div>
        
        <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <h4 style="color: #0d6efd; margin-top: 0;">🎁 Special Welcome Offer!</h4>
            <p style="font-size: 18px; margin: 10px 0;"><strong>Get 10% OFF</strong> on your first project</p>
            <p style="margin: 0;">Use code: <strong style="background: #ffc107; padding: 5px 10px; border-radius: 5px; color: #000;">WELCOME10</strong></p>
        </div>
        
        <p>Ready to start your web development project? <a href="https://websitedeveloper0002.in/contact.html" style="color: #0d6efd; text-decoration: none; font-weight: bold;">Contact us today!</a></p>
        
        <p style="margin-top: 30px;">Best regards,<br><strong>WebsiteDeveloper0002.in Team</strong></p>
        
        <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
        <p style="font-size: 12px; color: #666;">
            You are receiving this email because you subscribed to our newsletter at websitedeveloper0002.in<br>
            <a href="{{unsubscribe_url}}" style="color: #666;">Unsubscribe</a> | <a href="https://websitedeveloper0002.in/privacy-policy.html" style="color: #666;">Privacy Policy</a>
        </p>
    </div>
</body>
</html>',
'Hello {{name}},

Welcome to the WebsiteDeveloper0002.in newsletter family!

You have successfully subscribed and will receive:
- Weekly Web Development Tips
- Exclusive Offers and Discounts
- Industry Insights and SEO Tips
- Case Studies and Success Stories
- New Service Announcements

Special Welcome Offer: Get 10% OFF on your first project
Use code: WELCOME10

Ready to start your project? Visit: https://websitedeveloper0002.in/contact.html

Best regards,
WebsiteDeveloper0002.in Team

Unsubscribe: {{unsubscribe_url}}',
'["name", "unsubscribe_url"]');

-- Insert Default Admin User (password: admin123)
INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'super_admin');

-- Create Views for Easy Data Access
CREATE VIEW contact_summary AS
SELECT 
    id,
    CONCAT(first_name, ' ', last_name) as full_name,
    email,
    phone,
    company,
    package,
    status,
    created_at
FROM contact_submissions
ORDER BY created_at DESC;

CREATE VIEW newsletter_summary AS
SELECT 
    id,
    email,
    name,
    status,
    subscription_source,
    verified_at,
    created_at
FROM newsletter_subscribers
WHERE status = 'active'
ORDER BY created_at DESC;

-- Create Indexes for Performance
CREATE INDEX idx_email_queue_status_priority ON email_queue(status, priority, scheduled_at);
CREATE INDEX idx_analytics_date ON website_analytics(DATE(created_at));
CREATE INDEX idx_projects_status_date ON projects(project_status, created_at);
