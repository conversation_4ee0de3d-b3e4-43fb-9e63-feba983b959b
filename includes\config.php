<?php
/**
 * Database Configuration for WebsiteDeveloper0002.in
 * 
 * This file contains database connection settings and common functions
 */

// Prevent direct access
if (!defined('INCLUDED')) {
    define('INCLUDED', true);
}

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'websitedeveloper0002_db');
define('DB_USER', 'root'); // Change this for production
define('DB_PASS', ''); // Change this for production
define('DB_CHARSET', 'utf8mb4');

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com'); // Change to your SMTP server
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // Change to your email
define('SMTP_PASSWORD', 'your_email_password'); // Change to your password
define('SMTP_ENCRYPTION', 'tls');

// Site Configuration
define('SITE_NAME', 'WebsiteDeveloper0002.in');
define('SITE_URL', 'https://websitedeveloper0002.in');
define('ADMIN_EMAIL', '<EMAIL>');
define('SUPPORT_EMAIL', '<EMAIL>');
define('NO_REPLY_EMAIL', '<EMAIL>');

// Security Settings
define('ENCRYPTION_KEY', 'your-32-character-secret-key-here'); // Change this
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 minutes

// Error Reporting (Set to false in production)
define('DEBUG_MODE', true);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

/**
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            // First, try to create database if it doesn't exist
            $this->createDatabaseIfNotExists();

            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];

            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);

            // Create tables if they don't exist
            $this->createTablesIfNotExist();

        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("Database connection failed. Please try again later.");
            }
        }
    }

    private function createDatabaseIfNotExists() {
        try {
            $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
            $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo = null;
        } catch (PDOException $e) {
            // Database creation failed, but continue - might already exist
        }
    }

    private function createTablesIfNotExist() {
        $tables = [
            'contact_submissions' => "
                CREATE TABLE IF NOT EXISTS contact_submissions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    first_name VARCHAR(100) NOT NULL,
                    last_name VARCHAR(100) NOT NULL,
                    email VARCHAR(255) NOT NULL,
                    phone VARCHAR(20),
                    company VARCHAR(255),
                    package ENUM('basic', 'standard', 'premium', 'enterprise'),
                    services JSON,
                    budget VARCHAR(50),
                    message TEXT NOT NULL,
                    status ENUM('new', 'contacted', 'in_progress', 'completed', 'cancelled') DEFAULT 'new',
                    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_email (email),
                    INDEX idx_status (status),
                    INDEX idx_created_at (created_at)
                )
            ",
            'newsletter_subscribers' => "
                CREATE TABLE IF NOT EXISTS newsletter_subscribers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email VARCHAR(255) NOT NULL UNIQUE,
                    name VARCHAR(255),
                    status ENUM('pending', 'active', 'unsubscribed') DEFAULT 'pending',
                    verification_token VARCHAR(64),
                    unsubscribe_token VARCHAR(64) NOT NULL,
                    source VARCHAR(50) DEFAULT 'website',
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    verified_at TIMESTAMP NULL,
                    unsubscribed_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_email (email),
                    INDEX idx_status (status),
                    INDEX idx_verification_token (verification_token),
                    INDEX idx_unsubscribe_token (unsubscribe_token)
                )
            ",
            'email_queue' => "
                CREATE TABLE IF NOT EXISTS email_queue (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    to_email VARCHAR(255) NOT NULL,
                    to_name VARCHAR(255),
                    subject VARCHAR(500) NOT NULL,
                    html_content LONGTEXT,
                    text_content TEXT,
                    template_name VARCHAR(100),
                    template_data JSON,
                    status ENUM('pending', 'sending', 'sent', 'failed') DEFAULT 'pending',
                    priority TINYINT DEFAULT 5,
                    attempts TINYINT DEFAULT 0,
                    max_attempts TINYINT DEFAULT 3,
                    error_message TEXT,
                    scheduled_at TIMESTAMP NULL,
                    sent_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_status (status),
                    INDEX idx_scheduled_at (scheduled_at),
                    INDEX idx_priority (priority)
                )
            ",
            'admin_users' => "
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    email VARCHAR(255) NOT NULL UNIQUE,
                    password_hash VARCHAR(255) NOT NULL,
                    role ENUM('admin', 'manager', 'editor') DEFAULT 'admin',
                    is_active BOOLEAN DEFAULT TRUE,
                    last_login TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_username (username),
                    INDEX idx_email (email)
                )
            "
        ];

        foreach ($tables as $tableName => $sql) {
            try {
                $this->connection->exec($sql);
            } catch (PDOException $e) {
                // Table creation failed, but continue
                if (DEBUG_MODE) {
                    error_log("Failed to create table $tableName: " . $e->getMessage());
                }
            }
        }

        // Insert default admin user if not exists
        $this->createDefaultAdmin();
    }

    private function createDefaultAdmin() {
        try {
            $adminExists = $this->selectOne('admin_users', 'username = ?', ['admin']);

            if (!$adminExists) {
                $adminData = [
                    'username' => 'admin',
                    'email' => ADMIN_EMAIL,
                    'password_hash' => hashPassword('admin123'),
                    'role' => 'admin',
                    'is_active' => 1
                ];

                $this->insert('admin_users', $adminData);
            }
        } catch (Exception $e) {
            // Admin creation failed, but continue
            if (DEBUG_MODE) {
                error_log("Failed to create default admin: " . $e->getMessage());
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                throw new Exception("Query failed: " . $e->getMessage());
            } else {
                throw new Exception("Database query failed.");
            }
        }
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }
    
    public function select($table, $where = '', $params = [], $columns = '*') {
        $sql = "SELECT {$columns} FROM {$table}";
        if ($where) {
            $sql .= " WHERE {$where}";
        }
        
        return $this->query($sql, $params)->fetchAll();
    }
    
    public function selectOne($table, $where = '', $params = [], $columns = '*') {
        $sql = "SELECT {$columns} FROM {$table}";
        if ($where) {
            $sql .= " WHERE {$where}";
        }
        $sql .= " LIMIT 1";
        
        return $this->query($sql, $params)->fetch();
    }
}

/**
 * Utility Functions
 */

// Sanitize input data
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// Validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Validate phone number (Indian format)
function isValidPhone($phone) {
    $cleanPhone = preg_replace('/\D/', '', $phone);
    return preg_match('/^[6-9]\d{9}$/', $cleanPhone);
}

// Generate random token
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Hash password
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Verify password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Get client IP address
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

// Get user agent
function getUserAgent() {
    return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
}

// Log activity
function logActivity($action, $details = '', $userId = null) {
    $db = Database::getInstance();
    
    $logData = [
        'action' => $action,
        'details' => $details,
        'user_id' => $userId,
        'ip_address' => getClientIP(),
        'user_agent' => getUserAgent(),
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // Create activity_logs table if it doesn't exist
    $createTable = "
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            action VARCHAR(255) NOT NULL,
            details TEXT,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        )
    ";
    
    try {
        $db->query($createTable);
        $db->insert('activity_logs', $logData);
    } catch (Exception $e) {
        // Log to file if database logging fails
        error_log("Activity log failed: " . $e->getMessage());
    }
}

// Send JSON response
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Redirect with message
function redirectWithMessage($url, $message, $type = 'success') {
    session_start();
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit;
}

// Get flash message
function getFlashMessage() {
    session_start();
    if (isset($_SESSION['flash_message'])) {
        $message = [
            'text' => $_SESSION['flash_message'],
            'type' => $_SESSION['flash_type'] ?? 'info'
        ];
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return $message;
    }
    return null;
}

// Rate limiting
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 300) {
    $db = Database::getInstance();
    
    // Create rate_limits table if it doesn't exist
    $createTable = "
        CREATE TABLE IF NOT EXISTS rate_limits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            identifier VARCHAR(255) NOT NULL,
            attempts INT DEFAULT 1,
            first_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_identifier (identifier),
            INDEX idx_last_attempt (last_attempt)
        )
    ";
    
    try {
        $db->query($createTable);
        
        // Clean old entries
        $db->query("DELETE FROM rate_limits WHERE last_attempt < DATE_SUB(NOW(), INTERVAL ? SECOND)", [$timeWindow]);
        
        // Check current attempts
        $current = $db->selectOne('rate_limits', 'identifier = ?', [$identifier]);
        
        if (!$current) {
            // First attempt
            $db->insert('rate_limits', ['identifier' => $identifier]);
            return true;
        }
        
        if ($current['attempts'] >= $maxAttempts) {
            return false;
        }
        
        // Increment attempts
        $db->update('rate_limits', ['attempts' => $current['attempts'] + 1], 'identifier = ?', [$identifier]);
        return true;
        
    } catch (Exception $e) {
        // If rate limiting fails, allow the request
        return true;
    }
}

// Initialize database connection
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    if (DEBUG_MODE) {
        die("Database initialization failed: " . $e->getMessage());
    } else {
        die("Service temporarily unavailable. Please try again later.");
    }
}
?>
