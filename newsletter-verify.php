<?php
/**
 * Newsletter Verification Handler
 * WebsiteDeveloper0002.in
 */

// Include required files
require_once 'includes/config.php';
require_once 'includes/EmailHandler.php';

// Get verification token from URL
$token = sanitizeInput($_GET['token'] ?? '');

if (empty($token)) {
    $error = 'Invalid verification link.';
} else {
    try {
        $db = Database::getInstance();
        $emailHandler = new EmailHandler();
        
        // Find subscriber with this token
        $subscriber = $db->selectOne(
            'newsletter_subscribers', 
            'verification_token = ? AND status = ?', 
            [$token, 'pending']
        );
        
        if (!$subscriber) {
            $error = 'Invalid or expired verification link.';
        } else {
            // Verify the subscription
            $updateData = [
                'status' => 'active',
                'verified_at' => date('Y-m-d H:i:s'),
                'verification_token' => null,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $db->update('newsletter_subscribers', $updateData, 'id = ?', [$subscriber['id']]);
            
            // Send welcome email
            $emailHandler->sendNewsletterWelcome(
                $subscriber['email'], 
                $subscriber['name'], 
                $subscriber['unsubscribe_token']
            );
            
            $success = true;
            $email = $subscriber['email'];
            $name = $subscriber['name'];
            
            logActivity('newsletter_verified', "Email: {$subscriber['email']}, ID: {$subscriber['id']}");
        }
        
    } catch (Exception $e) {
        logActivity('newsletter_verify_error', $e->getMessage());
        $error = 'An error occurred while verifying your subscription.';
    }
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($success) ? 'Subscription Verified' : 'Verification Error'; ?> - WebsiteDeveloper0002.in</title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-code"></i> WebsiteDeveloper0002.in
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.html">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="services.html">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="pricing.html">Pricing</a></li>
                    <li class="nav-item"><a class="nav-link" href="portfolio.html">Portfolio</a></li>
                    <li class="nav-item"><a class="nav-link" href="about.html">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.html">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <section class="py-5" style="margin-top: 80px; min-height: 70vh;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="text-center">
                        <?php if (isset($success)): ?>
                            <!-- Success Message -->
                            <div class="verification-success">
                                <div class="success-icon mb-4">
                                    <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                                </div>
                                <h1 class="display-5 fw-bold text-success mb-4">Subscription Verified! 🎉</h1>
                                <p class="lead mb-4">
                                    Thank you <strong><?php echo htmlspecialchars($name ?: 'for subscribing'); ?></strong>! 
                                    Your newsletter subscription has been successfully verified.
                                </p>
                                
                                <div class="alert alert-info" role="alert">
                                    <h5 class="alert-heading"><i class="fas fa-envelope"></i> What's Next?</h5>
                                    <ul class="text-start mb-0">
                                        <li>You'll receive a welcome email shortly</li>
                                        <li>Weekly web development tips and insights</li>
                                        <li>Exclusive offers and discounts</li>
                                        <li>Latest industry trends and updates</li>
                                    </ul>
                                </div>
                                
                                <div class="welcome-offer mt-4 p-4 bg-warning rounded">
                                    <h4 class="text-dark mb-3">🎁 Welcome Offer!</h4>
                                    <p class="text-dark mb-3"><strong>Get 10% OFF</strong> on your first web development project</p>
                                    <p class="text-dark mb-3">Use code: <strong style="background: #fff; padding: 5px 10px; border-radius: 5px;">WELCOME10</strong></p>
                                    <a href="contact.html" class="btn btn-primary btn-lg">
                                        <i class="fas fa-rocket"></i> Start Your Project
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Error Message -->
                            <div class="verification-error">
                                <div class="error-icon mb-4">
                                    <i class="fas fa-exclamation-triangle text-danger" style="font-size: 5rem;"></i>
                                </div>
                                <h1 class="display-5 fw-bold text-danger mb-4">Verification Failed</h1>
                                <p class="lead mb-4"><?php echo htmlspecialchars($error); ?></p>
                                
                                <div class="alert alert-warning" role="alert">
                                    <h5 class="alert-heading"><i class="fas fa-lightbulb"></i> What You Can Do:</h5>
                                    <ul class="text-start mb-0">
                                        <li>Check if you clicked the correct link from your email</li>
                                        <li>Make sure the verification link hasn't expired</li>
                                        <li>Try subscribing again if the link is old</li>
                                        <li>Contact us if you continue to have issues</li>
                                    </ul>
                                </div>
                                
                                <div class="mt-4">
                                    <a href="index.html#newsletter" class="btn btn-primary btn-lg me-3">
                                        <i class="fas fa-redo"></i> Subscribe Again
                                    </a>
                                    <a href="contact.html" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-envelope"></i> Contact Support
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Navigation Links -->
                        <div class="mt-5 pt-4 border-top">
                            <h5 class="mb-3">Explore Our Services</h5>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <a href="services.html" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-cogs"></i><br>Our Services
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="pricing.html" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-tags"></i><br>Pricing Plans
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="portfolio.html" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-briefcase"></i><br>Our Work
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-code"></i> WebsiteDeveloper0002.in</h5>
                    <p>Complete web development solutions for Indian businesses. From domain to SEO - we've got you covered.</p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="services.html" class="text-white-50">Services</a></li>
                        <li><a href="pricing.html" class="text-white-50">Pricing</a></li>
                        <li><a href="portfolio.html" class="text-white-50">Portfolio</a></li>
                        <li><a href="contact.html" class="text-white-50">Contact</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +91 9876543210</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 WebsiteDeveloper0002.in. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <?php if (isset($success)): ?>
    <script>
        // Track successful verification
        console.log('Newsletter subscription verified successfully');
        
        // Google Analytics tracking
        if (typeof gtag !== 'undefined') {
            gtag('event', 'newsletter_verified', {
                'event_category': 'engagement',
                'event_label': 'newsletter_verification'
            });
        }
    </script>
    <?php endif; ?>
</body>
</html>
