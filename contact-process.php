<?php
/**
 * Contact Form Processing Script
 * WebsiteDeveloper0002.in
 * Enhanced with Database and Email Automation
 */

// Include required files
require_once 'includes/config.php';
require_once 'includes/EmailHandler.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Initialize response array
$response = array(
    'success' => false,
    'message' => '',
    'errors' => array()
);

// Check if form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    echo json_encode($response);
    exit;
}

// Rate limiting
$clientIP = getClientIP();
if (!checkRateLimit('contact_' . $clientIP, 3, 300)) { // 3 attempts per 5 minutes
    $response['message'] = 'Too many contact attempts. Please try again later.';
    echo json_encode($response);
    exit;
}

// Sanitize and validate input data
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validatePhone($phone) {
    $cleanPhone = preg_replace('/\D/', '', $phone);
    return preg_match('/^[6-9]\d{9}$/', $cleanPhone);
}

// Get form data
$firstName = sanitizeInput($_POST['firstName'] ?? '');
$lastName = sanitizeInput($_POST['lastName'] ?? '');
$email = sanitizeInput($_POST['email'] ?? '');
$phone = sanitizeInput($_POST['phone'] ?? '');
$company = sanitizeInput($_POST['company'] ?? '');
$package = sanitizeInput($_POST['package'] ?? '');
$services = $_POST['services'] ?? array();
$budget = sanitizeInput($_POST['budget'] ?? '');
$message = sanitizeInput($_POST['message'] ?? '');
$agree = isset($_POST['agree']);

// Validation
$errors = array();

if (empty($firstName)) {
    $errors['firstName'] = 'First name is required.';
}

if (empty($lastName)) {
    $errors['lastName'] = 'Last name is required.';
}

if (empty($email)) {
    $errors['email'] = 'Email is required.';
} elseif (!validateEmail($email)) {
    $errors['email'] = 'Please enter a valid email address.';
}

if (empty($phone)) {
    $errors['phone'] = 'Phone number is required.';
} elseif (!validatePhone($phone)) {
    $errors['phone'] = 'Please enter a valid Indian phone number.';
}

if (empty($message)) {
    $errors['message'] = 'Project details are required.';
}

if (!$agree) {
    $errors['agree'] = 'You must agree to the terms and conditions.';
}

// If there are validation errors, return them
if (!empty($errors)) {
    $response['errors'] = $errors;
    $response['message'] = 'Please fix the errors and try again.';
    echo json_encode($response);
    exit;
}

try {
    $db = Database::getInstance();
    $emailHandler = new EmailHandler();

    // Prepare contact data for database
    $contactData = [
        'first_name' => $firstName,
        'last_name' => $lastName,
        'email' => $email,
        'phone' => $phone,
        'company' => $company,
        'package' => $package,
        'services' => json_encode($services),
        'budget' => $budget,
        'message' => $message,
        'ip_address' => $clientIP,
        'user_agent' => getUserAgent(),
        'status' => 'new',
        'priority' => 'medium'
    ];

    // Insert into database
    $contactId = $db->insert('contact_submissions', $contactData);

    if (!$contactId) {
        throw new Exception('Failed to save contact submission');
    }

    // Send welcome email to customer
    $emailHandler->sendContactWelcome($contactData);

    // Prepare admin notification email
    $adminEmailData = [
        'contact_id' => $contactId,
        'name' => $firstName . ' ' . $lastName,
        'email' => $email,
        'phone' => $phone,
        'company' => $company,
        'package' => $package,
        'services' => implode(', ', $services),
        'budget' => $budget,
        'message' => $message,
        'ip_address' => $clientIP,
        'submission_time' => date('Y-m-d H:i:s')
    ];

    // Send admin notification
    sendAdminNotification($emailHandler, $adminEmailData);

    // Log successful submission
    logActivity('contact_form_submitted', "Contact ID: $contactId, Email: $email");

    $response['success'] = true;
    $response['message'] = 'Thank you! Your message has been sent successfully. We will contact you soon.';
    $response['contact_id'] = $contactId;

} catch (Exception $e) {
    logActivity('contact_form_error', $e->getMessage());
    $response['message'] = 'Sorry, there was an error sending your message. Please try again or contact us directly.';
}

/**
 * Send admin notification email
 */
function sendAdminNotification($emailHandler, $data) {
    $subject = "🚨 New Contact Form Submission - WebsiteDeveloper0002.in";

    $htmlContent = "
    <!DOCTYPE html>
    <html>
    <head><meta charset='UTF-8'><title>New Contact Submission</title></head>
    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background: #dc3545; color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;'>
            <h2 style='margin: 0;'>🚨 New Contact Form Submission</h2>
            <p style='margin: 10px 0 0;'>WebsiteDeveloper0002.in</p>
        </div>
        <div style='background: #f8f9fa; padding: 20px; border-radius: 0 0 10px 10px;'>
            <div style='background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h3 style='color: #dc3545; margin-top: 0;'>📋 Contact Details</h3>
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>Contact ID:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$data['contact_id']}</td></tr>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>Name:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$data['name']}</td></tr>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>Email:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'><a href='mailto:{$data['email']}'>{$data['email']}</a></td></tr>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>Phone:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'><a href='tel:{$data['phone']}'>{$data['phone']}</a></td></tr>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>Company:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$data['company']}</td></tr>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>Package:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ucfirst($data['package']) . "</td></tr>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>Budget:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$data['budget']}</td></tr>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>Services:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$data['services']}</td></tr>
                    <tr><td style='padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;'>IP Address:</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$data['ip_address']}</td></tr>
                    <tr><td style='padding: 8px; font-weight: bold;'>Submitted:</td><td style='padding: 8px;'>{$data['submission_time']}</td></tr>
                </table>
            </div>

            <div style='background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h3 style='color: #dc3545; margin-top: 0;'>💬 Message</h3>
                <div style='background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;'>
                    " . nl2br(htmlspecialchars($data['message'])) . "
                </div>
            </div>

            <div style='background: #e7f3ff; padding: 20px; border-radius: 8px; text-align: center;'>
                <h4 style='color: #0d6efd; margin-top: 0;'>⚡ Quick Actions</h4>
                <a href='mailto:{$data['email']}?subject=Re: Your inquiry - WebsiteDeveloper0002.in' style='background: #0d6efd; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>📧 Reply via Email</a>
                <a href='tel:{$data['phone']}' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>📞 Call Now</a>
                <a href='https://wa.me/{$data['phone']}' style='background: #25d366; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>💬 WhatsApp</a>
            </div>
        </div>
    </body>
    </html>";

    $textContent = "New Contact Form Submission - WebsiteDeveloper0002.in\n\n";
    $textContent .= "Contact ID: {$data['contact_id']}\n";
    $textContent .= "Name: {$data['name']}\n";
    $textContent .= "Email: {$data['email']}\n";
    $textContent .= "Phone: {$data['phone']}\n";
    $textContent .= "Company: {$data['company']}\n";
    $textContent .= "Package: " . ucfirst($data['package']) . "\n";
    $textContent .= "Budget: {$data['budget']}\n";
    $textContent .= "Services: {$data['services']}\n";
    $textContent .= "IP Address: {$data['ip_address']}\n";
    $textContent .= "Submitted: {$data['submission_time']}\n\n";
    $textContent .= "Message:\n{$data['message']}\n\n";
    $textContent .= "Reply: mailto:{$data['email']}\n";
    $textContent .= "Call: tel:{$data['phone']}";

    return $emailHandler->sendDirectEmail(
        ADMIN_EMAIL,
        'Admin',
        $subject,
        $htmlContent,
        $textContent
    );
}

// Return JSON response
echo json_encode($response);
?>
