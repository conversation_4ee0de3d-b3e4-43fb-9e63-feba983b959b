// Portfolio JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initPortfolioFilter();
    initLoadMore();
    initPortfolioAnimations();
});

// Portfolio Filter Functionality
function initPortfolioFilter() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter portfolio items
            filterPortfolioItems(portfolioItems, filter);
        });
    });
}

function filterPortfolioItems(items, filter) {
    items.forEach(item => {
        const category = item.getAttribute('data-category');
        
        if (filter === 'all' || category === filter) {
            item.classList.remove('hidden');
            // Add animation delay for staggered effect
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'scale(1)';
            }, Math.random() * 200);
        } else {
            item.classList.add('hidden');
            item.style.opacity = '0';
            item.style.transform = 'scale(0.8)';
        }
    });
}

// Load More Functionality
function initLoadMore() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const portfolioGrid = document.getElementById('portfolioGrid');
    
    if (!loadMoreBtn || !portfolioGrid) return;

    loadMoreBtn.addEventListener('click', function() {
        // Show loading state
        const originalText = this.innerHTML;
        this.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
        this.disabled = true;

        // Simulate loading delay
        setTimeout(() => {
            // Add more portfolio items
            addMorePortfolioItems(portfolioGrid);
            
            // Reset button
            this.innerHTML = originalText;
            this.disabled = false;
            
            // Hide button after loading more items (simulate end of content)
            this.style.display = 'none';
            
            // Show success message
            showToast('More projects loaded successfully!', 'success');
        }, 1500);
    });
}

function addMorePortfolioItems(container) {
    const newItems = [
        {
            category: 'business',
            icon: 'fas fa-industry',
            title: 'Manufacturing Pro',
            description: 'Industrial manufacturing website with product showcase और supplier portal',
            tags: ['Business', 'Manufacturing', 'B2B']
        },
        {
            category: 'ecommerce',
            icon: 'fas fa-tshirt',
            title: 'Style Store',
            description: 'Fashion e-commerce platform with size guide और virtual try-on feature',
            tags: ['E-commerce', 'Fashion', 'Virtual Try-on']
        },
        {
            category: 'healthcare',
            icon: 'fas fa-clinic-medical',
            title: 'MediCare Clinic',
            description: 'Medical clinic website with online consultation और prescription system',
            tags: ['Healthcare', 'Online Consultation', 'Prescription']
        }
    ];

    newItems.forEach((item, index) => {
        const portfolioItem = createPortfolioItem(item);
        container.appendChild(portfolioItem);
        
        // Add animation with delay
        setTimeout(() => {
            portfolioItem.style.opacity = '1';
            portfolioItem.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

function createPortfolioItem(item) {
    const div = document.createElement('div');
    div.className = 'col-lg-4 col-md-6 portfolio-item';
    div.setAttribute('data-category', item.category);
    div.style.opacity = '0';
    div.style.transform = 'translateY(30px)';
    div.style.transition = 'all 0.6s ease';
    
    div.innerHTML = `
        <div class="portfolio-card">
            <div class="portfolio-image">
                <div class="image-placeholder">
                    <i class="${item.icon}"></i>
                    <div class="overlay">
                        <a href="#" class="btn btn-light btn-sm">
                            <i class="fas fa-eye"></i> View Project
                        </a>
                    </div>
                </div>
            </div>
            <div class="portfolio-content">
                <h5>${item.title}</h5>
                <p>${item.description}</p>
                <div class="portfolio-tags">
                    ${item.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            </div>
        </div>
    `;
    
    return div;
}

// Portfolio Animations
function initPortfolioAnimations() {
    // Animate portfolio stats on scroll
    const stats = document.querySelectorAll('.portfolio-stat h3');
    
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    stats.forEach(stat => {
        observer.observe(stat);
    });

    // Animate portfolio items on scroll
    animatePortfolioItems();
}

function animateCounter(element) {
    const target = parseInt(element.textContent.replace(/[^\d]/g, ''));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60fps
    let current = 0;

    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            element.textContent = formatCounterValue(target);
            clearInterval(timer);
        } else {
            element.textContent = formatCounterValue(Math.ceil(current));
        }
    }, 16);
}

function formatCounterValue(value) {
    if (value >= 1000) {
        return Math.floor(value / 100) * 100 + '+';
    }
    return value + (value === 100 ? '%' : '+');
}

function animatePortfolioItems() {
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    portfolioItems.forEach((item, index) => {
        // Set initial state
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = `all 0.6s ease ${index * 0.1}s`;
        
        observer.observe(item);
    });
}

// Portfolio Item Click Handlers
document.addEventListener('DOMContentLoaded', function() {
    // Handle portfolio item clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('.portfolio-card')) {
            const card = e.target.closest('.portfolio-card');
            const title = card.querySelector('h5').textContent;
            
            // Add click animation
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = '';
            }, 150);
            
            // You can add modal or redirect logic here
            console.log('Portfolio item clicked:', title);
        }
    });
});

// Technology Items Animation
document.addEventListener('DOMContentLoaded', function() {
    const techItems = document.querySelectorAll('.tech-item');
    
    const observerOptions = {
        threshold: 0.2,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    techItems.forEach((item, index) => {
        // Set initial state
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = `all 0.5s ease ${index * 0.1}s`;
        
        observer.observe(item);
    });
});

// Search Functionality (if needed in future)
function initPortfolioSearch() {
    const searchInput = document.getElementById('portfolioSearch');
    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const portfolioItems = document.querySelectorAll('.portfolio-item');
        
        portfolioItems.forEach(item => {
            const title = item.querySelector('h5').textContent.toLowerCase();
            const description = item.querySelector('p').textContent.toLowerCase();
            const tags = Array.from(item.querySelectorAll('.tag')).map(tag => tag.textContent.toLowerCase());
            
            const isMatch = title.includes(searchTerm) || 
                          description.includes(searchTerm) || 
                          tags.some(tag => tag.includes(searchTerm));
            
            if (isMatch) {
                item.style.display = 'block';
                item.classList.remove('hidden');
            } else {
                item.style.display = 'none';
                item.classList.add('hidden');
            }
        });
    });
}

// Utility function for toast notifications
function showToast(message, type = 'success') {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast');
    existingToasts.forEach(toast => toast.remove());

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}
