<?php
/**
 * Newsletter Unsubscribe Handler
 * WebsiteDeveloper0002.in
 */

// Include required files
require_once 'includes/config.php';

// Get unsubscribe token from URL
$token = sanitizeInput($_GET['token'] ?? '');
$confirmed = isset($_POST['confirm_unsubscribe']);

if (empty($token)) {
    $error = 'Invalid unsubscribe link.';
} else {
    try {
        $db = Database::getInstance();
        
        // Find subscriber with this token
        $subscriber = $db->selectOne(
            'newsletter_subscribers', 
            'unsubscribe_token = ? AND status = ?', 
            [$token, 'active']
        );
        
        if (!$subscriber) {
            $error = 'Invalid unsubscribe link or you are already unsubscribed.';
        } else {
            if ($confirmed) {
                // Process unsubscribe
                $updateData = [
                    'status' => 'unsubscribed',
                    'unsubscribed_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $db->update('newsletter_subscribers', $updateData, 'id = ?', [$subscriber['id']]);
                
                $success = true;
                $email = $subscriber['email'];
                
                logActivity('newsletter_unsubscribed', "Email: {$subscriber['email']}, ID: {$subscriber['id']}");
            }
        }
        
    } catch (Exception $e) {
        logActivity('newsletter_unsubscribe_error', $e->getMessage());
        $error = 'An error occurred while processing your request.';
    }
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($success) ? 'Unsubscribed Successfully' : 'Unsubscribe'; ?> - WebsiteDeveloper0002.in</title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-code"></i> WebsiteDeveloper0002.in
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.html">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="services.html">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="pricing.html">Pricing</a></li>
                    <li class="nav-item"><a class="nav-link" href="portfolio.html">Portfolio</a></li>
                    <li class="nav-item"><a class="nav-link" href="about.html">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.html">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <section class="py-5" style="margin-top: 80px; min-height: 70vh;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="text-center">
                        <?php if (isset($success)): ?>
                            <!-- Success Message -->
                            <div class="unsubscribe-success">
                                <div class="success-icon mb-4">
                                    <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                                </div>
                                <h1 class="display-5 fw-bold mb-4">Successfully Unsubscribed</h1>
                                <p class="lead mb-4">
                                    You have been successfully unsubscribed from our newsletter.
                                </p>
                                
                                <div class="alert alert-info" role="alert">
                                    <h5 class="alert-heading"><i class="fas fa-info-circle"></i> What This Means:</h5>
                                    <ul class="text-start mb-0">
                                        <li>You will no longer receive our newsletter emails</li>
                                        <li>You can resubscribe anytime from our website</li>
                                        <li>We may still send important account-related emails</li>
                                        <li>Your data will be kept as per our privacy policy</li>
                                    </ul>
                                </div>
                                
                                <div class="feedback-section mt-4 p-4 bg-light rounded">
                                    <h5 class="mb-3">We'd Love Your Feedback</h5>
                                    <p class="mb-3">Help us improve by telling us why you unsubscribed:</p>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary" onclick="submitFeedback('too_frequent')">
                                            Too many emails
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="submitFeedback('not_relevant')">
                                            Content not relevant
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="submitFeedback('other')">
                                            Other reason
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php elseif (isset($error)): ?>
                            <!-- Error Message -->
                            <div class="unsubscribe-error">
                                <div class="error-icon mb-4">
                                    <i class="fas fa-exclamation-triangle text-danger" style="font-size: 5rem;"></i>
                                </div>
                                <h1 class="display-5 fw-bold text-danger mb-4">Unsubscribe Failed</h1>
                                <p class="lead mb-4"><?php echo htmlspecialchars($error); ?></p>
                                
                                <div class="mt-4">
                                    <a href="contact.html" class="btn btn-primary btn-lg">
                                        <i class="fas fa-envelope"></i> Contact Support
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Confirmation Form -->
                            <div class="unsubscribe-confirm">
                                <div class="warning-icon mb-4">
                                    <i class="fas fa-exclamation-circle text-warning" style="font-size: 5rem;"></i>
                                </div>
                                <h1 class="display-5 fw-bold mb-4">Confirm Unsubscribe</h1>
                                <p class="lead mb-4">
                                    Are you sure you want to unsubscribe from our newsletter?
                                </p>
                                
                                <div class="alert alert-warning" role="alert">
                                    <h5 class="alert-heading"><i class="fas fa-info-circle"></i> You'll Miss Out On:</h5>
                                    <ul class="text-start mb-0">
                                        <li>Weekly web development tips and tutorials</li>
                                        <li>Exclusive discounts and special offers</li>
                                        <li>Latest industry trends and insights</li>
                                        <li>Free resources and tools</li>
                                        <li>Early access to new services</li>
                                    </ul>
                                </div>
                                
                                <div class="subscriber-info mb-4 p-3 bg-light rounded">
                                    <strong>Email:</strong> <?php echo htmlspecialchars($subscriber['email']); ?><br>
                                    <strong>Subscribed:</strong> <?php echo date('F j, Y', strtotime($subscriber['created_at'])); ?>
                                </div>
                                
                                <form method="POST" class="mt-4">
                                    <input type="hidden" name="confirm_unsubscribe" value="1">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <button type="submit" class="btn btn-danger btn-lg me-md-2">
                                            <i class="fas fa-times"></i> Yes, Unsubscribe Me
                                        </button>
                                        <a href="index.html" class="btn btn-success btn-lg">
                                            <i class="fas fa-heart"></i> Keep Me Subscribed
                                        </a>
                                    </div>
                                </form>
                                
                                <div class="alternative-options mt-4 p-4 bg-info text-white rounded">
                                    <h5 class="mb-3">Alternative Options</h5>
                                    <p class="mb-3">Instead of unsubscribing completely, you can:</p>
                                    <ul class="text-start mb-0">
                                        <li>Reduce email frequency (contact us)</li>
                                        <li>Choose specific topics only</li>
                                        <li>Pause emails temporarily</li>
                                    </ul>
                                    <a href="contact.html" class="btn btn-light btn-sm mt-2">
                                        <i class="fas fa-cog"></i> Manage Preferences
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Navigation Links -->
                        <div class="mt-5 pt-4 border-top">
                            <h5 class="mb-3">Explore Our Services</h5>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <a href="services.html" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-cogs"></i><br>Our Services
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="pricing.html" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-tags"></i><br>Pricing Plans
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="contact.html" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-envelope"></i><br>Contact Us
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-code"></i> WebsiteDeveloper0002.in</h5>
                    <p>Complete web development solutions for Indian businesses. From domain to SEO - we've got you covered.</p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="services.html" class="text-white-50">Services</a></li>
                        <li><a href="pricing.html" class="text-white-50">Pricing</a></li>
                        <li><a href="portfolio.html" class="text-white-50">Portfolio</a></li>
                        <li><a href="contact.html" class="text-white-50">Contact</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +91 9876543210</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 WebsiteDeveloper0002.in. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function submitFeedback(reason) {
            // Send feedback to server
            fetch('newsletter-feedback.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reason: reason,
                    email: '<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                alert('Thank you for your feedback!');
            })
            .catch(error => {
                console.error('Feedback error:', error);
            });
        }
    </script>
</body>
</html>
