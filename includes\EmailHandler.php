<?php
/**
 * <PERSON><PERSON> Handler Class for WebsiteDeveloper0002.in
 * Handles email sending, templates, and queue management
 */

require_once 'config.php';

class EmailHandler {
    private $db;
    private $smtpHost;
    private $smtpPort;
    private $smtpUsername;
    private $smtpPassword;
    private $smtpEncryption;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->smtpHost = SMTP_HOST;
        $this->smtpPort = SMTP_PORT;
        $this->smtpUsername = SMTP_USERNAME;
        $this->smtpPassword = SMTP_PASSWORD;
        $this->smtpEncryption = SMTP_ENCRYPTION;
    }
    
    /**
     * Send email using template
     */
    public function sendTemplateEmail($templateName, $toEmail, $toName, $variables = []) {
        try {
            // Get template
            $template = $this->getTemplate($templateName);
            if (!$template) {
                throw new Exception("Template not found: $templateName");
            }
            
            // Replace variables in template
            $subject = $this->replaceVariables($template['subject'], $variables);
            $htmlContent = $this->replaceVariables($template['html_content'], $variables);
            $textContent = $this->replaceVariables($template['text_content'], $variables);
            
            // Add to email queue
            return $this->addToQueue(
                $toEmail,
                $toName,
                $subject,
                $htmlContent,
                $textContent,
                $template['id']
            );
            
        } catch (Exception $e) {
            logActivity('email_template_error', "Template: $templateName, Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send direct email
     */
    public function sendDirectEmail($toEmail, $toName, $subject, $htmlContent, $textContent = '') {
        return $this->addToQueue($toEmail, $toName, $subject, $htmlContent, $textContent);
    }
    
    /**
     * Add email to queue
     */
    private function addToQueue($toEmail, $toName, $subject, $htmlContent, $textContent = '', $templateId = null) {
        try {
            $emailData = [
                'to_email' => $toEmail,
                'to_name' => $toName,
                'from_email' => $this->smtpUsername,
                'from_name' => SITE_NAME,
                'subject' => $subject,
                'html_content' => $htmlContent,
                'text_content' => $textContent,
                'template_id' => $templateId,
                'priority' => 'normal',
                'status' => 'pending'
            ];
            
            $emailId = $this->db->insert('email_queue', $emailData);
            
            // Try to send immediately
            $this->processSingleEmail($emailId);
            
            return $emailId;
            
        } catch (Exception $e) {
            logActivity('email_queue_error', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Process single email from queue
     */
    public function processSingleEmail($emailId) {
        try {
            $email = $this->db->selectOne('email_queue', 'id = ? AND status = ?', [$emailId, 'pending']);
            
            if (!$email) {
                return false;
            }
            
            // Update attempts
            $this->db->update('email_queue', 
                ['attempts' => $email['attempts'] + 1], 
                'id = ?', 
                [$emailId]
            );
            
            // Send email
            $result = $this->sendEmail(
                $email['to_email'],
                $email['to_name'],
                $email['subject'],
                $email['html_content'],
                $email['text_content'],
                $email['from_email'],
                $email['from_name']
            );
            
            if ($result) {
                // Mark as sent
                $this->db->update('email_queue', 
                    [
                        'status' => 'sent',
                        'sent_at' => date('Y-m-d H:i:s')
                    ], 
                    'id = ?', 
                    [$emailId]
                );
                
                logActivity('email_sent', "Email sent to: {$email['to_email']}");
                return true;
            } else {
                // Mark as failed if max attempts reached
                if ($email['attempts'] >= $email['max_attempts']) {
                    $this->db->update('email_queue', 
                        [
                            'status' => 'failed',
                            'error_message' => 'Max attempts reached'
                        ], 
                        'id = ?', 
                        [$emailId]
                    );
                }
                return false;
            }
            
        } catch (Exception $e) {
            // Update error message
            $this->db->update('email_queue', 
                [
                    'status' => 'failed',
                    'error_message' => $e->getMessage()
                ], 
                'id = ?', 
                [$emailId]
            );
            
            logActivity('email_send_error', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Process email queue
     */
    public function processQueue($limit = 10) {
        try {
            $emails = $this->db->query(
                "SELECT id FROM email_queue 
                 WHERE status = 'pending' 
                 AND attempts < max_attempts 
                 AND scheduled_at <= NOW() 
                 ORDER BY priority DESC, created_at ASC 
                 LIMIT ?", 
                [$limit]
            )->fetchAll();
            
            $processed = 0;
            foreach ($emails as $email) {
                if ($this->processSingleEmail($email['id'])) {
                    $processed++;
                }
                
                // Small delay to prevent overwhelming the SMTP server
                usleep(100000); // 0.1 second
            }
            
            return $processed;
            
        } catch (Exception $e) {
            logActivity('email_queue_process_error', $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Send email using PHP mail() or SMTP
     */
    private function sendEmail($toEmail, $toName, $subject, $htmlContent, $textContent = '', $fromEmail = '', $fromName = '') {
        try {
            $fromEmail = $fromEmail ?: $this->smtpUsername;
            $fromName = $fromName ?: SITE_NAME;
            
            // Use simple mail() function for now
            // In production, consider using PHPMailer or similar library for SMTP
            
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=UTF-8',
                "From: $fromName <$fromEmail>",
                "Reply-To: $fromEmail",
                'X-Mailer: PHP/' . phpversion()
            ];
            
            $to = $toName ? "$toName <$toEmail>" : $toEmail;
            
            return mail($to, $subject, $htmlContent, implode("\r\n", $headers));
            
        } catch (Exception $e) {
            throw new Exception("Email sending failed: " . $e->getMessage());
        }
    }
    
    /**
     * Get email template
     */
    private function getTemplate($templateName) {
        return $this->db->selectOne('email_templates', 'template_name = ? AND is_active = 1', [$templateName]);
    }
    
    /**
     * Replace variables in template
     */
    private function replaceVariables($content, $variables) {
        foreach ($variables as $key => $value) {
            if (is_array($value)) {
                $value = implode(', ', $value);
            }
            $content = str_replace("{{$key}}", $value, $content);
        }
        
        // Remove any unreplaced variables
        $content = preg_replace('/\{\{[^}]+\}\}/', '', $content);
        
        return $content;
    }
    
    /**
     * Send welcome email for contact form submission
     */
    public function sendContactWelcome($contactData) {
        $variables = [
            'first_name' => $contactData['first_name'],
            'package' => ucfirst($contactData['package'] ?: 'Not specified'),
            'budget' => $contactData['budget'] ?: 'Not specified',
            'services' => is_array($contactData['services']) ? implode(', ', $contactData['services']) : ($contactData['services'] ?: 'Not specified')
        ];
        
        return $this->sendTemplateEmail(
            'contact_welcome',
            $contactData['email'],
            $contactData['first_name'] . ' ' . $contactData['last_name'],
            $variables
        );
    }
    
    /**
     * Send newsletter welcome email
     */
    public function sendNewsletterWelcome($email, $name, $unsubscribeToken) {
        $unsubscribeUrl = SITE_URL . "/newsletter-unsubscribe.php?token=" . $unsubscribeToken;
        
        $variables = [
            'name' => $name ?: 'Subscriber',
            'unsubscribe_url' => $unsubscribeUrl
        ];
        
        return $this->sendTemplateEmail(
            'newsletter_welcome',
            $email,
            $name,
            $variables
        );
    }
    
    /**
     * Send newsletter verification email
     */
    public function sendNewsletterVerification($email, $name, $verificationToken) {
        $verificationUrl = SITE_URL . "/newsletter-verify.php?token=" . $verificationToken;
        
        $subject = "Please verify your newsletter subscription - " . SITE_NAME;
        
        $htmlContent = "
        <!DOCTYPE html>
        <html>
        <head><meta charset='UTF-8'><title>Verify Newsletter Subscription</title></head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='background: #0d6efd; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                <h1 style='margin: 0; font-size: 24px;'>📧 Verify Your Subscription</h1>
                <p style='margin: 10px 0 0;'>WebsiteDeveloper0002.in Newsletter</p>
            </div>
            <div style='background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;'>
                <p>Hello <strong>" . ($name ?: 'there') . "</strong>,</p>
                <p>Thank you for subscribing to our newsletter! Please click the button below to verify your email address:</p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='$verificationUrl' style='background: #ffc107; color: #000; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;'>
                        ✅ Verify My Subscription
                    </a>
                </div>
                
                <p>If the button doesn't work, copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 5px;'>$verificationUrl</p>
                
                <p>If you didn't subscribe to our newsletter, please ignore this email.</p>
                
                <p>Best regards,<br><strong>WebsiteDeveloper0002.in Team</strong></p>
            </div>
        </body>
        </html>";
        
        $textContent = "Hello " . ($name ?: 'there') . ",\n\nThank you for subscribing to our newsletter! Please verify your email address by visiting:\n\n$verificationUrl\n\nIf you didn't subscribe, please ignore this email.\n\nBest regards,\nWebsiteDeveloper0002.in Team";
        
        return $this->sendDirectEmail($email, $name, $subject, $htmlContent, $textContent);
    }
    
    /**
     * Get email queue statistics
     */
    public function getQueueStats() {
        try {
            $stats = $this->db->query("
                SELECT 
                    status,
                    COUNT(*) as count
                FROM email_queue 
                GROUP BY status
            ")->fetchAll();
            
            $result = [
                'pending' => 0,
                'sent' => 0,
                'failed' => 0,
                'cancelled' => 0
            ];
            
            foreach ($stats as $stat) {
                $result[$stat['status']] = (int)$stat['count'];
            }
            
            return $result;
            
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
?>
