<?php
/**
 * Database Installation Script
 * WebsiteDeveloper0002.in
 * 
 * Run this script once to set up the database
 */

// Include config
require_once '../includes/config.php';

// Set execution time limit
set_time_limit(300);

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Setup - WebsiteDeveloper0002.in</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>WebsiteDeveloper0002.in Database Setup</h1>
";

try {
    // Read SQL file
    $sqlFile = '../database/database.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if (!$sql) {
        throw new Exception("Could not read SQL file");
    }
    
    echo "<div class='step'><strong>Step 1:</strong> SQL file loaded successfully</div>";
    
    // Connect to database
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<div class='step'><strong>Step 2:</strong> Database connection established</div>";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    echo "<div class='step'><strong>Step 3:</strong> Found " . count($statements) . " SQL statements</div>";
    
    // Execute each statement
    $executed = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        try {
            if (trim($statement)) {
                $pdo->exec($statement);
                $executed++;
                
                // Show progress for major operations
                if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                    echo "<div class='info'>✓ Created table: {$matches[1]}</div>";
                } elseif (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                    echo "<div class='info'>✓ Inserted data into: {$matches[1]}</div>";
                } elseif (preg_match('/CREATE VIEW\s+(\w+)/i', $statement, $matches)) {
                    echo "<div class='info'>✓ Created view: {$matches[1]}</div>";
                } elseif (preg_match('/CREATE INDEX\s+(\w+)/i', $statement, $matches)) {
                    echo "<div class='info'>✓ Created index: {$matches[1]}</div>";
                }
            }
        } catch (PDOException $e) {
            $errors++;
            echo "<div class='error'>✗ Error executing statement: " . htmlspecialchars($e->getMessage()) . "</div>";
            
            // Continue with other statements
        }
    }
    
    echo "<div class='step'><strong>Step 4:</strong> Executed $executed statements successfully";
    if ($errors > 0) {
        echo " ($errors errors)";
    }
    echo "</div>";
    
    // Verify installation
    echo "<div class='step'><strong>Step 5:</strong> Verifying installation...</div>";
    
    $tables = [
        'contact_submissions',
        'newsletter_subscribers', 
        'email_templates',
        'email_queue',
        'website_analytics',
        'projects',
        'admin_users'
    ];
    
    $tablesCreated = 0;
    foreach ($tables as $table) {
        try {
            $result = $pdo->query("SELECT COUNT(*) FROM $table");
            if ($result !== false) {
                $count = $result->fetchColumn();
                echo "<div class='info'>✓ Table '$table' exists with $count records</div>";
                $tablesCreated++;
            }
        } catch (PDOException $e) {
            echo "<div class='error'>✗ Table '$table' not found or accessible</div>";
        }
    }
    
    // Check email templates
    try {
        $templateCount = $pdo->query("SELECT COUNT(*) FROM email_templates")->fetchColumn();
        echo "<div class='info'>✓ Email templates: $templateCount installed</div>";
    } catch (PDOException $e) {
        echo "<div class='error'>✗ Could not verify email templates</div>";
    }
    
    // Check admin user
    try {
        $adminCount = $pdo->query("SELECT COUNT(*) FROM admin_users")->fetchColumn();
        echo "<div class='info'>✓ Admin users: $adminCount created</div>";
        
        if ($adminCount > 0) {
            $admin = $pdo->query("SELECT username FROM admin_users LIMIT 1")->fetch();
            echo "<div class='info'>Default admin username: <strong>{$admin['username']}</strong></div>";
            echo "<div class='info'>Default admin password: <strong>admin123</strong> (Please change this!)</div>";
        }
    } catch (PDOException $e) {
        echo "<div class='error'>✗ Could not verify admin users</div>";
    }
    
    if ($tablesCreated === count($tables)) {
        echo "<div class='step success'><strong>✅ Installation Completed Successfully!</strong></div>";
        
        echo "<div class='step'>
            <h3>Next Steps:</h3>
            <ol>
                <li>Delete this setup directory for security</li>
                <li>Update database credentials in includes/config.php</li>
                <li>Update email settings in includes/config.php</li>
                <li>Set up cron job for email processing: <code>*/5 * * * * php " . dirname(__DIR__) . "/cron/process-email-queue.php</code></li>
                <li>Access admin panel: <a href='../admin/login.php'>Admin Login</a></li>
                <li>Test contact form and newsletter subscription</li>
            </ol>
        </div>";
        
        echo "<div class='step info'>
            <h3>Configuration Checklist:</h3>
            <ul>
                <li>✓ Database tables created</li>
                <li>✓ Email templates installed</li>
                <li>✓ Admin user created</li>
                <li>⚠️ Update email SMTP settings</li>
                <li>⚠️ Change default admin password</li>
                <li>⚠️ Set up SSL certificate</li>
                <li>⚠️ Configure cron jobs</li>
            </ul>
        </div>";
        
    } else {
        echo "<div class='step error'><strong>❌ Installation Incomplete</strong><br>";
        echo "Only $tablesCreated out of " . count($tables) . " tables were created successfully.</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='step error'><strong>❌ Installation Failed</strong><br>";
    echo "Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    
    echo "<div class='step'>
        <h3>Troubleshooting:</h3>
        <ul>
            <li>Check database connection settings in includes/config.php</li>
            <li>Ensure database user has CREATE, INSERT, SELECT privileges</li>
            <li>Check if database exists and is accessible</li>
            <li>Verify PHP PDO MySQL extension is installed</li>
        </ul>
    </div>";
}

echo "
    <div class='step'>
        <h3>Support:</h3>
        <p>If you need help with installation:</p>
        <ul>
            <li>Email: <EMAIL></li>
            <li>Phone: +91 9876543210</li>
            <li>Check documentation in README.md</li>
        </ul>
    </div>
    
    <div style='text-align: center; margin-top: 40px;'>
        <a href='../index.html' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>
            Go to Website
        </a>
        <a href='../admin/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>
            Admin Login
        </a>
    </div>
</body>
</html>";
?>
