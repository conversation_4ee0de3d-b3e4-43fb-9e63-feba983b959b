<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms & Conditions - WebsiteDeveloper0002.in</title>
    <meta name="description" content="Terms and conditions for web development services provided by WebsiteDeveloper0002.in">
    <meta name="robots" content="index, follow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-code"></i> WebsiteDeveloper0002.in
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.html">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="services.html">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="pricing.html">Pricing</a></li>
                    <li class="nav-item"><a class="nav-link" href="portfolio.html">Portfolio</a></li>
                    <li class="nav-item"><a class="nav-link" href="about.html">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.html">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header bg-primary text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="display-4 fw-bold mb-3">Terms & Conditions</h1>
                    <p class="lead">हमारी services का उपयोग करने से पहले कृपया ये नियम पढ़ें</p>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html" class="text-warning">Home</a></li>
                            <li class="breadcrumb-item active text-white">Terms & Conditions</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Terms Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="legal-content">
                        <div class="last-updated mb-4">
                            <small class="text-muted">Last Updated: January 1, 2024</small>
                        </div>

                        <h2>1. स्वीकृति (Acceptance)</h2>
                        <p>WebsiteDeveloper0002.in की services का उपयोग करके आप इन Terms & Conditions को स्वीकार करते हैं। यदि आप इन नियमों से सहमत नहीं हैं तो कृपया हमारी services का उपयोग न करें।</p>

                        <h2>2. सेवाएं (Services)</h2>
                        <h3>2.1 Web Development Services</h3>
                        <ul>
                            <li>Website design और development</li>
                            <li>Domain registration और hosting services</li>
                            <li>E-commerce solutions</li>
                            <li>SEO और digital marketing</li>
                            <li>Website maintenance और support</li>
                        </ul>

                        <h3>2.2 Service Delivery</h3>
                        <ul>
                            <li>सभी projects का timeline mutual agreement के अनुसार होगा</li>
                            <li>Delivery dates approximate हैं और circumstances के अनुसार बदल सकती हैं</li>
                            <li>Client की तरफ से delay होने पर timeline extend हो सकती है</li>
                        </ul>

                        <h2>3. भुगतान नीति (Payment Policy)</h2>
                        <h3>3.1 Payment Terms</h3>
                        <ul>
                            <li><strong>Advance Payment:</strong> Project start करने के लिए 50% advance payment आवश्यक है</li>
                            <li><strong>Final Payment:</strong> Project completion पर remaining 50% payment</li>
                            <li><strong>Payment Methods:</strong> UPI, Net Banking, Credit/Debit Card, Bank Transfer</li>
                            <li><strong>GST:</strong> सभी payments पर applicable GST add होगा</li>
                        </ul>

                        <h3>3.2 Refund Policy</h3>
                        <ul>
                            <li>Project start होने के बाद advance payment refundable नहीं है</li>
                            <li>यदि हम project deliver नहीं कर पाते तो full refund किया जाएगा</li>
                            <li>Partial work के लिए partial payment की जा सकती है</li>
                        </ul>

                        <h2>4. बौद्धिक संपदा (Intellectual Property)</h2>
                        <h3>4.1 Website Ownership</h3>
                        <ul>
                            <li>Full payment के बाद website का ownership client को transfer हो जाता है</li>
                            <li>Source code और design files client को provide किए जाएंगे</li>
                            <li>Third-party plugins/themes के लिए separate licenses हो सकते हैं</li>
                        </ul>

                        <h3>4.2 Portfolio Rights</h3>
                        <ul>
                            <li>हम आपकी website को अपने portfolio में showcase कर सकते हैं</li>
                            <li>Client के business details confidential रखे जाएंगे</li>
                            <li>यदि आप portfolio में inclusion नहीं चाहते तो inform करें</li>
                        </ul>

                        <h2>5. ग्राहक की जिम्मेदारियां (Client Responsibilities)</h2>
                        <h3>5.1 Content और Materials</h3>
                        <ul>
                            <li>Website के लिए accurate content provide करना</li>
                            <li>High-quality images और videos देना</li>
                            <li>Timely feedback और approvals देना</li>
                            <li>Domain और hosting credentials safely रखना</li>
                        </ul>

                        <h3>5.2 Legal Compliance</h3>
                        <ul>
                            <li>Provided content legal और copyright-free होना चाहिए</li>
                            <li>Business licenses और registrations valid होने चाहिए</li>
                            <li>Website content में कोई illegal material नहीं होना चाहिए</li>
                        </ul>

                        <h2>6. सहायता और रखरखाव (Support & Maintenance)</h2>
                        <h3>6.1 Free Support Period</h3>
                        <ul>
                            <li><strong>Basic Package:</strong> 3 months free support</li>
                            <li><strong>Standard Package:</strong> 6 months free support</li>
                            <li><strong>Premium Package:</strong> 1 year free support</li>
                        </ul>

                        <h3>6.2 Support Includes</h3>
                        <ul>
                            <li>Bug fixes और technical issues</li>
                            <li>Minor content updates</li>
                            <li>Security updates</li>
                            <li>Backup और restore services</li>
                        </ul>

                        <h3>6.3 Paid Support</h3>
                        <ul>
                            <li>Free period के बाद paid maintenance plans available हैं</li>
                            <li>Major changes और new features के लिए separate charges</li>
                            <li>Emergency support 24/7 available (charges apply)</li>
                        </ul>

                        <h2>7. देयता सीमा (Limitation of Liability)</h2>
                        <ul>
                            <li>हमारी liability project cost से अधिक नहीं होगी</li>
                            <li>Third-party services (hosting, domain) की problems के लिए हम responsible नहीं हैं</li>
                            <li>Data loss या business interruption के लिए हम liable नहीं हैं</li>
                            <li>Client को regular backups maintain करने की सलाह दी जाती है</li>
                        </ul>

                        <h2>8. गोपनीयता (Confidentiality)</h2>
                        <ul>
                            <li>Client की business information confidential रखी जाएगी</li>
                            <li>Login credentials और sensitive data secure रखा जाएगा</li>
                            <li>Third parties के साथ information share नहीं की जाएगी</li>
                            <li>Project completion के बाद भी confidentiality maintain की जाएगी</li>
                        </ul>

                        <h2>9. समझौता समाप्ति (Termination)</h2>
                        <h3>9.1 Client द्वारा Termination</h3>
                        <ul>
                            <li>Client किसी भी समय project terminate कर सकता है</li>
                            <li>Completed work के लिए payment करना होगा</li>
                            <li>Advance payment refundable नहीं है</li>
                        </ul>

                        <h3>9.2 हमारे द्वारा Termination</h3>
                        <ul>
                            <li>Payment default के case में project terminate हो सकता है</li>
                            <li>Unethical या illegal requirements के लिए project refuse कर सकते हैं</li>
                            <li>Client cooperation न मिलने पर project terminate हो सकता है</li>
                        </ul>

                        <h2>10. बल माजूर (Force Majeure)</h2>
                        <p>Natural disasters, government actions, internet outages, या अन्य uncontrollable circumstances के कारण service में delay या interruption के लिए हम responsible नहीं हैं।</p>

                        <h2>11. विवाद समाधान (Dispute Resolution)</h2>
                        <ul>
                            <li>सभी disputes को पहले mutual discussion से solve करने की कोशिश की जाएगी</li>
                            <li>यदि resolution नहीं होता तो mediation का सहारा लिया जाएगा</li>
                            <li>Legal disputes के लिए Noida, Uttar Pradesh की courts का jurisdiction होगा</li>
                            <li>Indian laws के अनुसार सभी matters handle किए जाएंगे</li>
                        </ul>

                        <h2>12. संशोधन (Modifications)</h2>
                        <p>हम इन Terms & Conditions को समय-समय पर update कर सकते हैं। Major changes के लिए clients को notify किया जाएगा। Website पर updated terms check करते रहें।</p>

                        <h2>13. संपर्क जानकारी (Contact Information)</h2>
                        <p>Terms & Conditions के बारे में कोई सवाल हो तो contact करें:</p>
                        <ul>
                            <li><strong>Email:</strong> <EMAIL></li>
                            <li><strong>Phone:</strong> +91 9876543210</li>
                            <li><strong>Address:</strong> 123 Tech Park, Sector 18, Noida, UP 201301</li>
                        </ul>

                        <div class="agreement-note mt-5 p-4 bg-light rounded">
                            <h4><i class="fas fa-info-circle text-primary"></i> महत्वपूर्ण सूचना</h4>
                            <p class="mb-0">हमारी services का उपयोग करके आप इन सभी terms को स्वीकार करते हैं। यदि कोई confusion है तो project start करने से पहले clarify कर लें।</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-code"></i> WebsiteDeveloper0002.in</h5>
                    <p>Complete web development solutions for Indian businesses. From domain to SEO - we've got you covered.</p>
                </div>
                <div class="col-md-4">
                    <h5>Legal</h5>
                    <ul class="list-unstyled">
                        <li><a href="terms-conditions.html" class="text-white-50">Terms & Conditions</a></li>
                        <li><a href="privacy-policy.html" class="text-white-50">Privacy Policy</a></li>
                        <li><a href="refund-policy.html" class="text-white-50">Refund Policy</a></li>
                        <li><a href="disclaimer.html" class="text-white-50">Disclaimer</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +91 9876543210</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 WebsiteDeveloper0002.in. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/main.js"></script>
</body>
</html>
